#!/bin/bash

#!/bin/bash

set -e

# Echo the registry URL
echo "Registry URL: $CI_REGISTRY"

# Perform non-interactive Docker login
echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"

# Verify the login by pulling the image
#docker pull "$CI_REGISTRY_IMAGE/kairo_api:latest"

# Remove the pulled image to clean up
#docker rmi "$CI_REGISTRY_IMAGE/kairo_api:latest"

echo "Successfully logged in to $CI_REGISTRY"
