# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Nodejs.gitlab-ci.yml

# Official framework image. Look for the different tagged releases at:
# https://hub.docker.com/r/library/node/tags/
image: docker:latest
services:
  - docker:27-dind
# Pick zero or more services to be used on all builds.
# Only needed when using a docker container to run your tests in.
# Check out: http://docs.gitlab.com/ee/ci/docker/using_docker_images.html#what-is-a-service


variables:
  GITLAB_BUILD: 1
  DOCKER_HOST: tcp://docker:2375
  # PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

default:
  before_script:
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json

stages:
  - test
  - push_images

test:
  image: python:3.12-bookworm
  services:
    - redis:latest
    - postgres:16
  variables:
    ENV: test
    KAIRO_ENV: test
    APP_PORT: 9005
    POSTGRES_DB: kairo_test
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
    KAIRO_APP_DIR: /app
    KAIRO_DOMAIN: localhost:9005
    KAIRO_JWT_SECRET: e15692e346ea4d079407e3ed1478d831
    KAIRO_DB_URL_ASYNC: postgresql+asyncpg://postgres:postgres@postgres:5432/kairo_test
    KAIRO_DB_URL: postgresql+psycopg2://postgres:postgres@postgres:5432/kairo_test
    KAIRO_REDIS_URL: redis://redis:6379/0
    POSTGRES_HOST_AUTH_METHOD: trust
  script:
    - pip install poetry
    - poetry install --no-root
    - poetry run alembic upgrade head
    - echo $(pwd)
    - cd /builds/aidolabs/quantsnap/kairo && PYTHONPATH=$(pwd) poetry run pytest -s tests/
  only:
    - branches
  except:
    - tags

push_images:
  image: python:3.12-bookworm
  stage: push_images
  resource_group: staging
  services:
    - docker:dind
  variables:
    DEPLOY_ENVIRONMENT: staging
    BRANCH_NAME: $CI_COMMIT_BRANCH
  only:
    - main
  script:
    - apt-get update && apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
    - curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    - echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    - apt-get update
    - apt-get install -y docker-ce docker-ce-cli containerd.io
    - docker --version
    - ./scripts/ci_docker_login.sh && make build-and-push
