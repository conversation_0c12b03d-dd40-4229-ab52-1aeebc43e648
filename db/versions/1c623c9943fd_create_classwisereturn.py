"""create_classwisereturn

Revision ID: 1c623c9943fd
Revises: 35ea7e907a89
Create Date: 2025-04-26 14:33:06.194796

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "1c623c9943fd"
down_revision: Union[str, None] = "35ea7e907a89"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "classwisereturn",
        sa.Column(
            "classcode",
            sa.<PERSON>Integer,
            primary_key=True,
            comment="Class Code (will take one of class codes of Category names from Sclass_mst table)",
        ),
        sa.Column(
            "classname",
            sa.VARCHAR(500),
            nullable=True,
            comment="Category Name (will take Category names from Sclass_mst table)",
        ),
        sa.Column("opt_code", sa.<PERSON>, primary_key=True, comment="Option Code"),
        sa.Column("date", sa.DateTime, nullable=True, comment="Date"),
        sa.Column(
            "1dayret", sa.Numeric(18, 3), nullable=True, comment="One Day Return"
        ),
        sa.Column(
            "1weekret", sa.Numeric(18, 3), nullable=True, comment="One Week Return"
        ),
        sa.Column(
            "2weekret", sa.Numeric(18, 3), nullable=True, comment="Two Week Return"
        ),
        sa.Column(
            "3weekret", sa.Numeric(18, 3), nullable=True, comment="Three Week Return"
        ),
        sa.Column(
            "1monthret", sa.Numeric(18, 3), nullable=True, comment="One Month Return"
        ),
        sa.Column(
            "2monthret", sa.Numeric(18, 3), nullable=True, comment="Two Month Return"
        ),
        sa.Column(
            "3monthret", sa.Numeric(18, 3), nullable=True, comment="Three Month Return"
        ),
        sa.Column(
            "6monthret", sa.Numeric(18, 3), nullable=True, comment="Six Month Return"
        ),
        sa.Column(
            "9mnthret", sa.Numeric(18, 3), nullable=True, comment="Nine Month Return"
        ),
        sa.Column(
            "1yearret", sa.Numeric(18, 3), nullable=True, comment="One Year Return"
        ),
        sa.Column(
            "2yearret", sa.Numeric(18, 3), nullable=True, comment="Two Year Return"
        ),
        sa.Column(
            "3yearret", sa.Numeric(18, 3), nullable=True, comment="Three Year Return"
        ),
        sa.Column(
            "4yearret", sa.Numeric(18, 3), nullable=True, comment="Four Year Return"
        ),
        sa.Column(
            "5yearret", sa.Numeric(18, 3), nullable=True, comment="Five Year Return"
        ),
        sa.Column(
            "incret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="Return on inception date",
        ),
        sa.Column("ytdret", sa.Numeric(18, 3), nullable=True, comment="YTD Return"),
        sa.Column(
            "1wschemecode", sa.BigInteger, nullable=True, comment="1 Week Schemecode"
        ),
        sa.Column(
            "weekhighret", sa.Numeric(18, 3), nullable=True, comment="Week High Return"
        ),
        sa.Column(
            "1mschemecode", sa.BigInteger, nullable=True, comment="1 Month Schemecode"
        ),
        sa.Column(
            "monthhighret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="Month High Return",
        ),
        sa.Column(
            "3mschemecode", sa.BigInteger, nullable=True, comment="3 Month Schemecode"
        ),
        sa.Column(
            "3monthhighret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="3 Month High Return",
        ),
        sa.Column(
            "6mschemecode", sa.BigInteger, nullable=True, comment="6 Month Schemecode"
        ),
        sa.Column(
            "6monthhighret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="6 Month High Return",
        ),
        sa.Column(
            "1yschemecode", sa.BigInteger, nullable=True, comment="1 Year Schemecode"
        ),
        sa.Column(
            "1yhighret", sa.Numeric(18, 3), nullable=True, comment="1 Year High Return"
        ),
        sa.Column(
            "3yschemecode", sa.BigInteger, nullable=True, comment="3 Year Schemecode"
        ),
        sa.Column(
            "3yhighret", sa.Numeric(18, 3), nullable=True, comment="3 Year High Return"
        ),
        sa.Column(
            "5yschemecode", sa.BigInteger, nullable=True, comment="5 Year Schemecode"
        ),
        sa.Column(
            "5yhighret", sa.Numeric(18, 3), nullable=True, comment="5 Year High Return"
        ),
        sa.Column(
            "incretschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Inception Return Schemecode",
        ),
        sa.Column(
            "increthighret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="Inception High Return",
        ),
        sa.Column(
            "worst1wschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst 1week Schemecode",
        ),
        sa.Column(
            "weekworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="Week Worst Return",
        ),
        sa.Column(
            "worst1mschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst 1Month Schemecode",
        ),
        sa.Column(
            "monthworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="Month Worst Return",
        ),
        sa.Column(
            "worst3mschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst 3 Month Schemecode",
        ),
        sa.Column(
            "3monthworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="3 Month Worst Return",
        ),
        sa.Column(
            "worst6mschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst 6 Month Schemecode",
        ),
        sa.Column(
            "6monthworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="6 Month Worst Return",
        ),
        sa.Column(
            "worst1yschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst 1 Year Schemecode",
        ),
        sa.Column(
            "1yworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="1 Year Worst Return",
        ),
        sa.Column(
            "worst3yschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst 3 Year Schemecode",
        ),
        sa.Column(
            "3yworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="3 Year Worst Return",
        ),
        sa.Column(
            "worst5yschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst 5 Year Schemecode",
        ),
        sa.Column(
            "5yworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="5 Year Worst Return",
        ),
        sa.Column(
            "worstincretschemecode",
            sa.BigInteger,
            nullable=True,
            comment="Worst Inception Schemecode",
        ),
        sa.Column(
            "incretworstret",
            sa.Numeric(18, 3),
            nullable=True,
            comment="Inception Worst Return",
        ),
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),
        # The source specifies 'classcode' and 'opt_code' as Primary Keys. [4]
        # For time-series data that might be daily, including 'date' in the primary
        # key is common practice to ensure uniqueness per category/option per day.
        # However, adhering strictly to the source, the PK is only 'classcode' and 'opt_code'.
        # If daily uniqueness is required, consider adding 'date' to the PrimaryKeyConstraint.
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("classwisereturn")
