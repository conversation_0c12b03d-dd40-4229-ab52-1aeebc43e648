"""loadtype_mst

Revision ID: baf81ab65cfa
Revises: f48495fe615d
Create Date: 2025-03-09 17:27:04.900307

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "baf81ab65cfa"
down_revision: Union[str, None] = "f48495fe615d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "loadtype_mst",
        sa.Column("ltypecode", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("load", sa.String(length=20), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="loadtype_mst_ltypecode_idx",
        table_name="loadtype_mst",
        columns=["ltypecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("loadtype_mst")
