#!/bin/bash

# Function to check if the tunnel is running
check_tunnel() {
    pgrep -f "ssh -L $LOCAL_PORT:$DB_HOST:$DB_PORT.*$VPS_HOST" >/dev/null
    return $?
}

# Function to start the tunnel
start_tunnel() {
    # Prompt for SSH username
    read -p "Enter your SSH username: " SSH_USER
    if [ -z "$SSH_USER" ]; then
        echo "Error: SSH username cannot be empty."
        exit 1
    fi

    if check_tunnel; then
        echo "Tunnel is already running."
        exit 1
    fi
    ssh -L "$LOCAL_PORT:$DB_HOST:$DB_PORT" -N -f "$SSH_USER@$VPS_HOST"
    if [ $? -eq 0 ]; then
        echo "Tunnel started. Connect to the database at localhost:$LOCAL_PORT"
    else
        echo "Failed to start tunnel. Check your SSH key, username, or network."
        exit 1
    fi
}

# Function to stop the tunnel
stop_tunnel() {
    if ! check_tunnel; then
        echo "No tunnel is running."
        exit 1
    fi
    TUNNEL_PID=$(pgrep -f "ssh -L $LOCAL_PORT:$DB_HOST:$DB_PORT.*$VPS_HOST")
    kill "$TUNNEL_PID"
    if [ $? -eq 0 ]; then
        echo "Tunnel stopped."
    else
        echo "Failed to stop tunnel."
        exit 1
    fi
}

# Main logic
case "$1" in
start)
    start_tunnel
    ;;
stop)
    stop_tunnel
    ;;
*)
    echo "Usage: $0 {start|stop}"
    echo "  start: Start the database tunnel"
    echo "  stop: Stop the database tunnel"
    exit 1
    ;;
esac
