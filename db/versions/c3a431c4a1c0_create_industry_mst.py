"""create_industry_mst

Revision ID: c3a431c4a1c0
Revises: 8a19a4922f15
Create Date: 2025-03-09 17:30:26.191115

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "c3a431c4a1c0"
down_revision: Union[str, None] = "8a19a4922f15"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "industry_mst",
        sa.Column("ind_code", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("industry", sa.String(length=255), nullable=True),
        sa.Column("ind_shortname", sa.String(length=255), nullable=True),
        sa.Column("sector", sa.String(length=255), nullable=True),
        sa.Column("sector_code", sa.Integer(), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="industry_mst_ind_code_idx",
        table_name="industry_mst",
        columns=["ind_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("industry_mst")
