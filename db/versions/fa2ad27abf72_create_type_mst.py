"""create_type_mst

Revision ID: fa2ad27abf72
Revises: d242b419c4d1
Create Date: 2025-03-09 16:40:36.870564

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "fa2ad27abf72"
down_revision: Union[str, None] = "d242b419c4d1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "type_mst",
        sa.Column("type_code", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("type", sa.String(length=50), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="type_mst_type_code_idx",
        table_name="type_mst",
        columns=["type_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="type_mst_type_idx",
        table_name="type_mst",
        columns=["type"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("type_mst")
