"""create_fvchange

Revision ID: 2fd2b4ef2779
Revises: 42d983bd4891
Create Date: 2025-04-27 11:21:58.501533

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "2fd2b4ef2779"
down_revision: Union[str, None] = "42d983bd4891"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the Fvchange table based on the provided schema.
    This table contains schemes Face value change information.
    """
    op.create_table(
        "fvchange",
        sa.Column(
            "amc_code",
            sa.BigInteger,
            nullable=True,
            comment="Accord Fintech’s AMC Code is unique for each company",
        ),  # [2]
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Schemecode",
        ),  # [2]
        sa.Column(
            "scheme_name", sa.VARCHAR(255), nullable=True, comment="Scheme Name"
        ),  # [2]
        sa.Column(
            "fvbefore", sa.Numeric(18, 6), nullable=True, comment="Face Value Before"
        ),  # [3]
        sa.Column(
            "fvafter", sa.Numeric(18, 6), nullable=True, comment="Face Value After"
        ),  # [3]
        sa.Column(
            "fvdate", sa.DateTime, primary_key=True, comment="Face Value Date"
        ),  # [3]
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),  # [3]
        # Primary key is composite: schemecode and fvdate based on the source
        sa.PrimaryKeyConstraint("schemecode", "fvdate"),  # [2, 3]
    )


def downgrade():
    """
    Drops the Fvchange table.
    """
    op.drop_table("fvchange")
