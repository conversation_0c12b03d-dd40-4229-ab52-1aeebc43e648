"""create_amc_master_new

Revision ID: bfafe89c0a56
Revises: 6ac3629d0295
Create Date: 2025-03-09 15:06:44.164293

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "bfafe89c0a56"
down_revision: Union[str, None] = "6ac3629d0295"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "amc_mst_new",
        sa.Column(
            "amc_code",
            sa.BigInteger(),
            primary_key=True,
            nullable=False,
            autoincrement=False,
        ),
        sa.Column("amc", sa.String(length=255), nullable=True),
        sa.Column("fund", sa.String(length=255), nullable=True),
        sa.Column("srno", sa.<PERSON>(), nullable=True),
        sa.Column("office_type", sa.String(length=60), nullable=True),
        sa.Column("add1", sa.Text(), nullable=True),  # Using Text for nVarchar(max)
        sa.Column("add2", sa.Text(), nullable=True),  # Using Text for nVarchar(max)
        sa.Column("add3", sa.Text(), nullable=True),  # Using Text for nVarchar(max)
        sa.Column("email", sa.String(length=255), nullable=True),
        sa.Column("phone", sa.String(length=255), nullable=True),
        sa.Column("fax", sa.String(length=255), nullable=True),
        sa.Column(
            "webiste", sa.String(length=255), nullable=True
        ),  # Note: 'webiste' might be a typo for 'website'
        sa.Column("setup_date", sa.DateTime(), nullable=True),
        sa.Column("mf_type", sa.String(length=255), nullable=True),
        sa.Column("trustee_name", sa.String(length=255), nullable=True),
        sa.Column(
            "sponsor_name", sa.Text(), nullable=True
        ),  # Using Text for nVarchar(max)
        sa.Column("amc_inc_date", sa.DateTime(), nullable=True),
        sa.Column("s_name", sa.String(length=50), nullable=True),
        sa.Column("amc_symbol", sa.String(length=50), nullable=True),
        sa.Column("city", sa.String(length=255), nullable=True),
        sa.Column("rtamccode", sa.String(length=100), nullable=True),
        sa.Column("rtamccode_1", sa.String(length=100), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        schema=None,
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="amc_mst_new_mf_type_idx",
        table_name="amc_mst_new",
        columns=["mf_type"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="amc_mst_new_amc_symbol_idx",
        table_name="amc_mst_new",
        columns=["amc_symbol"],
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_table("amc_mst_new", schema=None)
