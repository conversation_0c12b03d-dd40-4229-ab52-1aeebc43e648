import os
import json
import csv
import asyncpg
from typing import Dict, Any, List, Optional
from os import Path<PERSON><PERSON>
from config.cfg import cfg
from config.logger import log_info, log_debug, log_error
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text


class TableService:
    """Service to read a JSON file and convert it to a CSV file.
    It also provides methods to get the table data and column names.
    The JSON file is expected to be in the format:
    {
        "Table": [
            {
                "Column1": "Value1",
                "Column2": "Value2",
                ...
            },
            ...
        ]
    }
    The CSV file will be saved in the same folder as the JSON file with the same name.
    The CSV file will have the same column names as the JSON file.
    The CSV file will be saved in the folder specified by the folder_name parameter.
    The folder_name parameter is expected to be a path-like object.
    The CSV file will be saved in the folder_name/csv folder.
    The CSV file will be named <database_table_name>.csv where database_table_name is the name of the JSON file without the .txt extension.
    """

    def __init__(self, filename: <PERSON><PERSON>ike, folder_name: <PERSON><PERSON>ike) -> None:
        self.filename: PathLike = filename
        self.database_table_name: str = (
            str(filename).lower().replace(".txt", "").split("/")[-1]
        )
        self.csv_folder_name: str = f"{folder_name}/csv"
        self.csv_file_path: PathLike = os.path.join(
            self.csv_folder_name, f"{self.database_table_name}.csv"
        )
        self.table_data: List[Dict[str, Any]] = []
        self.column_names: List[str] = []

    async def __call__(self):
        await self.invoke()

    async def invoke(self):
        await self.get_table_data()
        await self.get_column_names()

    async def get_table_data(self) -> List[Dict[str, Any]]:
        """Get the table data from the JSON file.
        The JSON file is expected to be in the format:
        {
            "Table": [
                {
                    "Column1": "Value1",
                    "Column2": "Value2",
                    ...
                },
                ...
            ]
        }

        Returns:
            List[Dict[str, Any]]: The table data as a list of dictionaries.
        """
        with open(self.filename, "r") as f:
            data: Dict[str, Any] = json.load(f)
            self.table_data: List[Dict[str, Any]] = data.get("Table", [])
        return self.table_data

    async def get_column_names(self) -> List[str]:
        """Get the column names from the table data.
        The column names are the keys of the first row of the table data.

        Returns:
            List[str]: The column names as a list of strings.
        If the table data is empty, an empty list is returned.
        If the table data is not empty, the column names are sorted and returned.
        """
        if not self.table_data:
            await self.get_table_data()

        first_row: Optional[Dict[str, Any]] = self.table_data[0]

        if not first_row:
            self.column_names = []
        self.column_names = sorted(list(first_row.keys()))
        return self.column_names


class DbWriteService:
    """Service to write data to a database."""

    def __init__(self, db_session: AsyncSession) -> None:
        self.db_session: AsyncSession = db_session

    async def check_table_exists(self, table_name: str) -> bool:
        """Check if the table exists in the database.
        The table name is expected to be in lowercase.
        The table name is expected to be in the format <database_table_name>.

        Args:
            table_name (str): The name of the table to check.
        The table name is expected to be in lowercase.

        Returns:
            bool: True if the table exists, False otherwise.
        If the table name is not in lowercase, the table name is converted to lowercase.
        """

        result = await self.db_session.execute(
            text(
                f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='{table_name}')"
            )
        )
        exists = result.scalar()
        return exists

    async def check_table_empty(self, table_name: str) -> bool:
        """Check if the table is empty.
        The table name is expected to be in lowercase.

        Args:
            table_name (str): The name of the table to check.
        The table name is expected to be in lowercase.

        Returns:
            bool: True if the table is empty, False otherwise.
        """
        result = await self.db_session.execute(
            text(f"SELECT COUNT(*) FROM {table_name}")
        )
        count = result.scalar()
        return count == 0

    async def write_to_db(self, table_name: str, csv_file_path: str):
        """Write the data to the database.

        Args:
            table_name (str): The name of the table to write to.
            csv_file_path (str): The path to the CSV file to write to.
        """
        log_info(f"Coping filename: {csv_file_path} to database")

        with open(csv_file_path, mode="r") as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)  # first row is the header
            column_list: str = ", ".join(headers)

        column_list: str = ",".join(
            [f'"{c.lower().strip()}"' for c in column_list.split(",")]
        )

        # Use COPY with explicit column list
        copy_sql: str = (
            f"COPY {table_name} ({column_list}) "
            f"FROM STDIN WITH (FORMAT CSV, HEADER) "
            f"DELIMITER ',' CSV HEADER"
        )

        log_info(msg=f"INSERT SQL: f{copy_sql}")

        # Use asyncpg to copy the CSV file to the database
        try:
            conn: asyncpg.Connection = await asyncpg.connect(
                cfg("DB_URL_ASYNC_IMPORTER")
            )
            try:
                with open(csv_file_path, "rb") as f:
                    result = await conn.copy_to_table(
                        table_name,
                        source=f,
                        columns=[c.lower().strip() for c in headers],
                        format="csv",
                        delimiter=",",
                        header=True,
                    )
                    log_info(f"Successfully copied {csv_file_path} to {table_name}")
                    await self.db_session.commit()
            finally:
                await conn.close()
        except Exception as e:
            log_info(f"Error copying {csv_file_path} to {table_name}: {e}")
            raise e


class HistoricalImportService:
    """Service to import historical data from a folder of JSON files."""

    def __init__(
        self,
        db_session: AsyncSession,
        folder_name: PathLike = "mf",
        table_service: type[TableService] = TableService,
        db_write_service: type[DbWriteService] = DbWriteService,
    ) -> None:
        self.db_session: AsyncSession = db_session
        self.folder_name: PathLike = folder_name
        self.csv_folder: Optional[PathLike] = f"{self.folder_name}/csv"
        self.table_service: type[TableService] = table_service
        self.db_write_service: DbWriteService = db_write_service(db_session=db_session)

    async def invoke(self):
        await self.create_csv_folder_if_not_exists()
        await self.convert_to_csv()
        await self.write_to_db()

    async def create_csv_folder_if_not_exists(self):
        """Create a folder to save the CSV files."""
        if not os.path.exists(self.csv_folder):
            os.makedirs(self.csv_folder)
            log_info(msg=f"Created CSV folder: {self.csv_folder}")
            return

        log_info(msg=f"CSV folder already exists: {self.csv_folder}")

    async def convert_to_csv(self):
        """Convert JSON files to CSV files."""
        for entry in sorted(os.scandir(self.folder_name), key=lambda e: e.name):
            if entry.is_file() and entry.name.endswith(
                ".txt"
            ):  # Assuming JSON files are .txt
                text_file_path: str = entry.path
                table_svc: TableService = self.table_service(
                    folder_name=self.folder_name, filename=text_file_path
                )
                await table_svc.invoke()

                self.csv_folder = table_svc.csv_folder_name

                table_name: str = table_svc.database_table_name

                table_name: str = entry.name.lower().replace(".txt", "")

                table_data: List[Dict[str, Any]] = table_svc.table_data
                if not table_data:
                    continue

                # Write to CSV
                with open(table_svc.csv_file_path, "w", newline="") as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=table_svc.column_names)
                    writer.writeheader()
                    writer.writerows(table_data)

                log_info(
                    msg=f"Txt filename: {entry.name}, table_name: {table_name}, csv_file_path: {table_svc.csv_file_path}"
                )

    @classmethod
    async def get_sanitized_tablename(cls, table_filename: str) -> str:
        """Extracts the correct table_name for tables that are made up of
        multiple extended files to get the correct table.

        Args:
        table_filename (str): The filename to extract the table name from.

        Raises:
            e: Raised if the tablename does not contain a numeric extension
                Ex: navhist_02.csv
        Returns:
            str: The sanitized table name.
        """

        split_table_filename: List[str] = table_filename.split("_")

        if len(split_table_filename) > 1:
            try:
                table_name_with_integer_extension: int = int(split_table_filename[1])
                if isinstance(table_name_with_integer_extension, int):
                    table_name = split_table_filename[0]
                    return table_name
            except ValueError as e:
                return table_filename
            except Exception as e:
                log_info(f"Error copying {table_filename} to {table_name}: {e}")
                raise e
        return table_filename

    async def write_to_db(self):
        """Write the CSV files to the database."""

        for entry in sorted(os.scandir(self.csv_folder), key=lambda e: e.name):
            log_info(f"Coping filename: {entry.name} to database")
            if entry.is_file() and entry.name.endswith(".csv"):

                table_name: str = entry.name.replace(".csv", "")
                table_name: str = await self.get_sanitized_tablename(
                    table_filename=table_name
                )
                csv_file_path: str = entry.path

                log_info(
                    msg=f"Copying {csv_file_path} to {table_name} table in database"
                )
                if not await self.db_write_service.check_table_exists(
                    table_name=table_name
                ):
                    log_info(f"Table {table_name} does not exists in database")
                    continue

                if not await self.db_write_service.check_table_empty(
                    table_name=table_name
                ):
                    if not table_name == "navhist":
                        log_info(f"Table {table_name} is not empty")
                        continue

                await self.db_write_service.write_to_db(
                    table_name=table_name, csv_file_path=csv_file_path
                )


if __name__ == "__main__":
    import asyncio
    import argparse
    from config.cfg import cfg
    from connectors.async_pg import get_async_session

    async def main(
        folder_name: PathLike = "/Users/<USER>/projects/adiolabs/kairo/local_data/mf/20250228",
    ):

        folder_name: PathLike = folder_name
        log_info(f"Starting import for folder: {folder_name}")
        db_session: AsyncSession = await get_async_session()
        service: HistoricalImportService = HistoricalImportService(
            db_session=db_session, folder_name=folder_name
        )
        await service.invoke()

    parser = argparse.ArgumentParser(description="Import historical data")
    parser.add_argument(
        "--folder_name",
        type=str,
        default=cfg("MF_HISTORICAL_DATA_FOLDER"),
        help="Folder name to import data from",
    )
    args = parser.parse_args()
    print(args)
    asyncio.run(main(args.folder_name))
