#!/bin/bash
set -e
echo "Push Registry URL: $KAIRO_REGISTRY_URL"
echo "Push Registry Image URL: $CI_REGISTRY_IMAGE"
GIT_COMMIT_ID=$(git rev-parse HEAD)

if [[ "$1" == "local" ]]; then
    echo "Pushing to local: Registry URL: ${KAIRO_REGISTRY_URL}/kairo_api:${GIT_COMMIT_ID}"
    docker push ${KAIRO_REGISTRY_URL}/kairo_api:${GIT_COMMIT_ID}
    echo "Pushing to local: Registry URL: ${KAIRO_REGISTRY_URL}:latest"
    docker push ${KAIRO_REGISTRY_URL}/kairo_api:latest
else
    echo "Pushing to CI/CD: Registry URL: $CI_REGISTRY_IMAGE:${GIT_COMMIT_ID}"
    docker push ${CI_REGISTRY_IMAGE}/kairo_api:${GIT_COMMIT_ID}

    echo "Pushing to CI/CD: Registry URL: $CI_REGISTRY_IMAGE/kairo_api:latest"
    docker push $CI_REGISTRY_IMAGE/kairo_api:latest
fi
