"""create_sclass_mst

Revision ID: 88b0b8c6cb37
Revises: 175b30ab3c89
Create Date: 2025-03-09 16:44:42.884564

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "88b0b8c6cb37"
down_revision: Union[str, None] = "175b30ab3c89"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "sclass_mst",
        sa.Column("classcode", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("classname", sa.String(length=500), nullable=True),
        sa.Column("asset_code", sa.BigInteger(), nullable=True),
        sa.Column("asset_type", sa.String(length=500), nullable=True),
        sa.Column("category", sa.String(length=500), nullable=True),
        sa.Column("sub_category", sa.String(length=500), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="sclass_mst_classcode_idx",
        table_name="sclass_mst",
        columns=["classcode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="sclass_mst_classname_idx",
        table_name="sclass_mst",
        columns=["classname"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="sclass_mst_asset_code_idx",
        table_name="sclass_mst",
        columns=["asset_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="sclass_mst_asset_type_idx",
        table_name="sclass_mst",
        columns=["asset_type"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="sclass_mst_category_idx",
        table_name="sclass_mst",
        columns=["category"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="sclass_mst_sub_category_idx",
        table_name="sclass_mst",
        columns=["sub_category"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("sclass_mst")
