## Environments

The application uses

1. Python 3.12.1
2. Postgres 16
3. Redis 7

The application runs on 4 environments

1. development - Your local dev environment
2. local - Your local test environment
3. test - CI/CD tests on gitlab
4. production - Production.

The configuration for each of these environments is managed by the files in the `environments` folder

1. development.env
2. local.env
3. test.env
4. template.env - This is a sample file indicating the environment variables required to run the service.

All .env files should include the variables present in template.env. You may replace the values present in `local.env` to ensure you can configure you local test environment to your needs.

Do not commit the `local.env` or the `development.env` file to Gitlab.

## Pre-Requisites

### Install A Postgresql client

You can install any PostgreSQL client or install the Postgres.app on MacOS or just PostgresSQL on linux.

Ensure the Postgres server is turned off as we'll be running the database in a container for development.

### Install `jq`

For macOS

```bash
$ brew install jq
```

For Ubuntu/Debian

```bash
sudo apt-get install jq
```

## Setting Up Environments

Copy the values in template.env and create 2 files

1. development.env
2. local.env

Here is how the how your development.env should look

```env
PYTHONPATH=/root/folder/of/your/app
KAIRO_ENV=development
APP_PORT=9002
KAIRO_DOMAIN=localhost:9002
KAIRO_DB_URL=postgresql+psycopg2://postgres:@localhost:5432/kairo_development
KAIRO_DB_URL_ASYNC=postgresql+asyncpg://postgres:@localhost:5432/kairo_development
KAIRO_REDIS_URL=redis://localhost:6379/0
KAIRO_MF_HISTORICAL_DATA_FOLDER=/path/to/your/historical/folder
```

### Toggling Between Environments.

The script is a helper which loads the environment in a subshell. Add this script to `.bashrc` or `.bash_profile` to load environments manually before running `pytest` or switching environments

```bash
loadenv()
{
         echo "Loading $1"
         for i in $(cat $1 | grep "^[^#;]"); do
            export $i
          done
}
```

After adding this to the `.bashrc` or `.bash_profile` reload your environment by running

```bash
$ source ~/.bashrc
```

or

```bash
$ source ~/.bash_profile
```

Now, you can toggle environments using the following commands

```bash
# load test environment
$ loadenv environments/local.env
# check env vars
$ echo $KAIRO_DB_URL_ASYNC
postgresql+asyncpg://kairo_db_user:KAIRO_db_password@localhost:5432/kairo_test_local

# load development environment
$ loadenv environments/development.env
# check env vars
$ echo $KAIRO_DB_URL_ASYNC
postgresql+asyncpg://KAIRO_db_user:KAIRO_db_password@localhost:5432/KAIRO_development
```

## Setup Local For Tests And Development

To setup development environment run

```bash
$ make setup-local
```

This does the following things

1. Creates a local image for Postgres with plugins
2. Creates a local image for Redis with AOF turned on.
3. Generates the setup.sql for test database creation.
4. Creates the test database.
5. Migrates the test database.

## Run Tests

Load your test environment using

```bash
loadenv enviornments/local.env
```

And then you can run tests by running

```bash
$ make tests
```

## Setup infra

> This is not essential and can be completely skipped unless you want to save the images to a container registry.

This setups images for Postgres with pgvector and Redis. The infra setup performs the following steps.

1. Creates a Postgres image
2. Creates a Redis image
3. Uploads the Postgres image to our container registry
4. Uploads the Redis image to our container registry

To just access all the images locally run

```bash
$ make setup-infra-local
```

To push infra to the container registry use

```bash
$ make setup-infra
```

## Launch Development Environment

1. Load the development configurations

```bash
$ loadenv environments/development.env
```

2. Launch containers using docker-compose

```bash
$ make dev-up
```

Your development environment should be up and running. We can test this by invoking the healthcheck endpoint using cURL.

```bash
$ curl --location 'http://localhost:9002/'
```

```json
{
  "data": {
    "service": "kairo_service",
    "time": "2024-10-12T10:16:30.488344+05:30",
    "version": "1.0"
  },
  "api_version": "1.0",
  "errors": {
    "msg": [],
    "code": null
  }
}
```

## Local Data

Your local data is stored in a folder called `data/postgres` and `data/redis`.

## Setting Up Development Environment For Code

### Installations

1. Install [pyenv](https://github.com/pyenv/pyenv) for Python version management and virtualenv management
2. Install [poetry](https://python-poetry.org/) for dependency management
3. Install project dependencies

### Create a virtual environment

```bash
$ pyenv virtualenv 3.12.1 kairo
$ pyenv activate kairo
```

In some scenarios this may suggest that you upgrade `pip`.

### Install poetry

```bash
$ pip install poetry
```

This ensures that we're running poetry only within this environment.

### Install dependencies

```bash
$ poetry install
```

### Run migrations

```bash
$ loadenv environments/development.env
$ poetry run alembic upgrade head
```

### Launch application

```bash
$ ./launch.sh
```

## Transactional Unit Tests

## Understanding Transactional Tests

We use transactional tests instead of resetting the database on each call. This is managed by the code in `tests/conftest.py`

if a test needs database access ensure the **fixture** `async_db_session` is passed as an argument after import the `tests/conftest.py` file.

The `db_session` method does the following things

1. It launches a database transaction before starting a test.
2. It launches a nested-transaction to allow us to call `commit` within our code.
3. It keeps an event handler looking for closing of a nested-transaction and re-launches a new nest-transaction
4. It closes the transaction and the session at the end of each test function.

```python
@pytest.fixture(scope="function")
def db_session():
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingLocalSession(bind=connection)
    nested = connection.begin_nested()

    @event.listens_for(session, "after_transaction_end")
    def end_savepoint(session, transaction):
        nonlocal nested

        if not nested.is_active:
            nested = connection.begin_nested()

    yield session

    if nested.is_active:
        nested.rollback()
    transaction.rollback()
    connection.close()
```

A sample test looks like this

```python
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from src.models import model


class TestUserService:
    @pytest.mark.asyncio
    async def test_build_and_create(self, async_db_session: AsyncSession):
        from src.domains.user.service import UserService, User

        user: User = await UserService.build_and_create(
            email="<EMAIL>",
            db_session=async_db_session,
            password="password",
            first_name="Sid",
            last_name="Ravichandran",
        )

        assert user.id is not None
        assert user.email == "<EMAIL>"
        assert user.first_name == "Sid"
        assert user.last_name == "Ravichandran"
        assert user.timezone == "Asia/Kolkata"
        assert user.is_blocked is False
        assert user.verified_at is None
        assert user.user_metadata == {}
        assert user.created_at is not None
        assert user.encrypted_password is not None
```

## Async Controller Tests and Database Session Middleware management

The app uses dependency injection to inject a connection object at the start of each request using an middleware. In `web_app.py`

```python
from time import time
from typing import Callable, Any, Optional
from fastapi.requests import Request
from starlette.types import ASGIApp
from starlette.middleware.base import BaseHTTPMiddleware
from connectors.async_postgres import get_async_session, async_scoped_session
from sqlalchemy.ext.asyncio import AsyncSession


class DbMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable):
        """
        Dispatches the request to the next middleware or endpoint, while managing the database session.

        Args:
            request (Request): The incoming request.
            call_next (Callable): The next middleware or endpoint to call.

        Returns:
            Any: The response from the next middleware or endpoint.
        """
        db: AsyncSession = await get_async_session()
        try:
            request.state.db = db
            response: Any = await call_next(request)
        finally:
            # Uncomment for async API
            if request.state.db and request.state.db.is_active:
                if isinstance(request.state.db, AsyncSession):
                    db_session: AsyncSession = request.state.db
                    await db_session.close()
                else:
                    request.state.db.close()
        return response

```

This ensures a database connection object is created and closed at the end of each request.

### Transactional Tests

However, our tests use transactional tests so we need to ensure that while running tests the application loads a test db connection and lets the test fixture manage the opening and closing of the connection.

As before we manage transactional tests using the `db_session` fixture in `tests/conftest.py`

## Branching with Alembic

It may happen at times when we merge from another branch the migrations fail as they have multiple heads. This requires us to merge the heads in migrations just like in code

```bash
$ poetry run alembic branches
```

OR

```bash
$ poetry run alembic heads --verbose
```

You'll notice if your migrations have multiple heads preventing a smooth sequential migration. To resolve this we create a merge file

```bash
$ poetry run alembic merge heads
```

This creates an empty merge migration

```bash
$ poetry run alembic upgrade head
```

Your migrations should run now.

Repeat the same steps for the test environment.

## Deployment And Monitoring

The project uses Docker Swarm as the container orchestrator and uses [Dokploy](https://dokploy.com/) as the platform to deploy it. Dokploy is completely open source and offers complete control over the infrastructure and service.

### Building Container Images

> You're never expected to build and push from your local machine. Always rely on the CI to build and push the latest container images.

#### When Do We Build And Push

Presently, images are built and pushed to the container registy when a feature branch is merged to main.

Gitlab CI builds the images and pushes it to the container registry.

#### Local

In order to build container images locally you're expected to run.

```bash
$ ./scripts/infra/app/build.sh local
```

#### Push

```bash
$ ./scripts/infra/app/push.sh local
```

#### CI

Tests run for all branches. When the code is merged to `main`, the CI pulls up the latest code on the main branch for a build.

1. This is managed using the configurations on the `.gitlab-ci.yml`
2. The code is pushed to our container registry which is `gitlab`

#### Deploying

1. We use Dokploy interface to deploy code. The following images represent the interface used to push the images.
2. Presently, the system doesn't use tagged releases but once we migrate to production we can start using tagged releases.

To be added.

## Importing Historical Data

In order to import historical data ensure the migrations have been run

```python
$ poetry run alembic upgrade head
```

You can import historical data by running either of these commands.

```python
$ poetry run python src/services/importers/mf/historical_import_service.py --folder_name=/Users/<USER>/Code/PythonApps/Quantsnap/kairo/local_data/mf/20250228
```

### Or Use the Launch Script

In order to use this ensure the environment variable called `KAIRO_MF_HISTORICAL_DATA_FOLDER` has been set to the folder that contains the historical data, for your `development.env`.

Ensure you load the environment file by running

```bash
$ loadenv environments/development.env
```

Now, load the actual data

```bash
$ ./launch_historical_import_service.sh
```

### Accessing The Database Locally

The file placed in `scripts/tunnel.sh` creates a remote tunnel to the database.

> The database is located in a publicly inaccessible VPC and can be accessed by servers that are part of a secure VPC. In order to access the data we enable a tunnel that connects to one of the servers in our cluster, which in turn proxies the database connection to the PostgreSQL instance.

#### How Do I Access The Data?

> This works on a Mac, Linux and Windows (WSL2)

1. This requires you to share your public access key located usually in `/home/<USER>/.ssh/id_rsa.pub`
2. Your `environment/development.env` file should contain the following environment variables

```env
VPS_HOST=<cluster_vps_ip>
DB_HOST=<db_url_shared_privately>
DB_PORT=<db_port>
LOCAL_PORT=5433
SSH_KEY=/Users/<USER>/.ssh/id_rsa.pub
```

3. Once added ensure you run `loadenv environments/development.env` to activate the envronment variables.
4. Run the command to start your tunnel session

```bash
$ ./script/tunnel.sh start
Enter your SSH username: sid
Tunnel started. Connect to the database at localhost:5433
```

3. Now you can access the DB with any database client by connecting to localhost port 5433. You will have a database username and password allocated to you.

To access the datbase connect to

```bash
➜  $ psql -U sid_readonly -h localhost -p 5433 -d kairo_production
   Password for user sid_readonly:
```

Note that the Redis URL is a rediss://URL
