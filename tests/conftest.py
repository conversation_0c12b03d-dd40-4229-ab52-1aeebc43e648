import asyncio
import pytest
import pytest_asyncio
import sqlalchemy
from sqlalchemy import event, text
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
)
from sqlalchemy.orm import sessionmaker, scoped_session
from config.cfg import cfg


metadata = sqlalchemy.MetaData()

async_engine = create_async_engine(
    cfg("DB_URL_ASYNC"),
    pool_size=10,
    echo=False,
    max_overflow=10,
    pool_use_lifo=True,
    pool_recycle=100,
)
TestingAsyncSessionLocal = sessionmaker(
    async_engine,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
    class_=AsyncSession,
)  # type: ignore


@pytest.fixture(scope="session")
def event_loop():
    """
    Creates an instance of the default event loop for the test session.
    """
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def async_db_session():
    """The expectation with async_sessions is that the
    transactions be called on the connection object instead of the
    session object.

    Detailed explanation of async transactional tests
    https://github.com/sqlalchemy/sqlalchemy/issues/5811
    """
    async with async_engine.connect() as connection:
        trans = await connection.begin()
        async_session = TestingAsyncSessionLocal(bind=connection)
        nested = await connection.begin_nested()

        @event.listens_for(async_session.sync_session, "after_transaction_end")
        def end_savepoint(session, transaction):
            nonlocal nested

            if connection.closed:
                return

            if not connection.in_nested_transaction():
                nested = connection.sync_connection.begin_nested()

        yield async_session
        await trans.rollback()
        await async_session.close()
        await connection.close()
