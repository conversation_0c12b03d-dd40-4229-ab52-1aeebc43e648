from typing import Optional, Any, TypeVar, Generic, List, Dict
from uuid import UUID, uuid4
from pydantic import BaseModel, ConfigDict
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi import Request, Header, Depends, status
from config.logger import log_debug, log_error
from json.decoder import J<PERSON>NDecodeError
from utils.datetime_support import date_to_datetime, date, datetime
from src.exceptions import UnauthorizedException, ApiException


T = TypeVar("T")

API_VERSION = "1.0"


class ResponseErrorsDAO(BaseModel, Generic[T]):
    msg: List[str] = []
    code: Optional[str] = None
    model_config = ConfigDict(
        title="errors",
        arbitrary_types_allowed=True,
        extra="ignore",
        from_attributes=True,
        json_encoders={
            UUID: lambda o: str(o),
            date: lambda v: date_to_datetime(dt=v).isoformat(timespec="microseconds"),
            datetime: lambda v: v.isoformat(timespec="microseconds"),
        },
    )


class SuccessResponseDAO(BaseModel, Generic[T]):
    data: Optional[T | Dict] = {}
    api_version: str = API_VERSION
    errors: ResponseErrorsDAO = ResponseErrorsDAO()
    model_config = ConfigDict(
        title="success_response",
        arbitrary_types_allowed=True,
        extra="ignore",
        from_attributes=True,
        json_encoders={
            UUID: lambda o: str(o),
            date: lambda v: date_to_datetime(dt=v).isoformat(timespec="microseconds"),
            datetime: lambda v: v.isoformat(timespec="microseconds"),
        },
    )


class FailureResponseDAO(BaseModel, Generic[T]):
    data: Optional[T | Dict] = {}
    api_version: str = API_VERSION
    errors: ResponseErrorsDAO
    model_config = ConfigDict(
        title="failure_response",
        arbitrary_types_allowed=True,
        extra="ignore",
        from_attributes=True,
        json_encoders={
            date: lambda v: date_to_datetime(dt=v).isoformat(timespec="microseconds"),
            datetime: lambda v: v.isoformat(timespec="microseconds"),
        },
    )


def success_response(
    payload: T,
    api_version: str = API_VERSION,
    errors: Optional[ResponseErrorsDAO] = None,
) -> dict[str, Any]:
    if not errors:
        errors = ResponseErrorsDAO()

    success_response_dao = SuccessResponseDAO(
        api_version=api_version, data=payload, errors=errors
    )
    return jsonable_encoder(success_response_dao)


def failure_response(
    error_code: str,
    errors: list[str] = [],
    payload: Optional[T] = {},
    api_version: str = API_VERSION,
) -> Dict[str, Any]:
    failure_response_dao = FailureResponseDAO(
        api_version=api_version,
        data=payload,
        errors=ResponseErrorsDAO(
            msg=errors,
            code=error_code,
        ),
    )
    return jsonable_encoder(failure_response_dao)


def validation_error_failure_response(
    exc: RequestValidationError,
    error_id: str = None,
    request_id: str = None,
    error_code: str = None,
):
    error_messages = []
    for err in exc.raw_errors:
        if type(err) == list:
            _msgs: list[str] = []
            for error in err:
                _msg: str = "".join([str(e["msg"]) for e in error.exc.errors()])
                _msgs.append(_msg)
            error_messages = [",".join(_msgs)]

        else:
            errors = err.exc.errors()
            error_messages = []
            for error in errors:
                if error["loc"] and error["loc"][0] == "__root__":
                    error_messages.append(f'{error["msg"]}')
                else:
                    error_messages.append(f'{error["loc"][0]}: {error["msg"]}')

    if not error_code:
        error_code = "unprocessable_entity"

    error_messages = [". ".join(error_messages)]
    log_error(
        msg=f"error_id={error_id} rid={request_id} RequestValidationError: {exc}", e=exc
    )
    return failure_response(
        errors=error_messages,
        error_code=error_code,
        payload={"error_id": error_id, "request_id": request_id},
    )


async def log_request_body(request: Request):
    content_type = request.headers.get("content-type", None)
    path = f'"{request.method} {request.url}"'
    if (
        content_type
        and content_type == "application/json"
        and (not request.method == "GET" and not request.method == "DELETE")
    ):
        _body = await request.json()
        log_debug(msg=f"rid={request.state.x_request_id} path={path} params={_body}")
        request.state.body_params = _body
    return request


async def current_user(request: Request, authorization: str = Header(None)):
    if not authorization:
        raise UnauthorizedException(msg="Unauthorized")

    try:
        token = authorization.split()[-1]
        user: UserDAO = await AuthService.verify(db_session=request.state.db, jwt=token)
        if not user:
            raise UnauthorizedException(
                msg="Unauthorized", http_error_code=status.HTTP_401_UNAUTHORIZED
            )
    except ValueError as e:
        log_error(msg=f"Error verifying token: {e}", e=e)
        raise UnauthorizedException(
            msg="Unauthorized", http_error_code=status.HTTP_401_UNAUTHORIZED
        )
    return user
