#!/bin/bash

echo "Build Registry URL: $KAIRO_REGISTRY_URL"
echo "Build Registry Image URL: $CI_REGISTRY_IMAGE"
GIT_COMMIT_ID=$(git rev-parse HEAD)

if [[ "$1" == "local" ]]; then
    echo "Building for local: Registry URL: kairo_api:${GIT_COMMIT_ID}"
    docker build -t kairo_api:${GIT_COMMIT_ID} -f docker/app/Dockerfile .

    echo "Building for local: Registry URL: kairo_api:latest"
    docker build -t kairo_api:latest -f docker/app/Dockerfile .
else
    echo "Building for CI/CD: Registry URL: $CI_REGISTRY_IMAGE:${GIT_COMMIT_ID}"
    docker build -t "$CI_REGISTRY_IMAGE/kairo_api:${GIT_COMMIT_ID}" --platform=linux/amd64 -f docker/app/Dockerfile .

    echo "Building for CI/CD: Registry URL: $CI_REGISTRY_IMAGE:latest"
    docker build -t "$CI_REGISTRY_IMAGE/kairo_api:latest" --platform=linux/amd64 -f docker/app/Dockerfile .
fi
