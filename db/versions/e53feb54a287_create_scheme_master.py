"""create_scheme_master

Revision ID: e53feb54a287
Revises: 3d74867ada37
Create Date: 2025-03-09 16:01:23.077824

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e53feb54a287"
down_revision: Union[str, None] = "3d74867ada37"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_master",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            primary_key=True,
            nullable=False,
            autoincrement=False,
        ),
        sa.Column(
            "amc_code",
            sa.BigInteger(),
            nullable=True,
        ),
        sa.Column("scheme_name", sa.String(length=255), nullable=True),
        sa.Column("color", sa.String(length=50), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_master_schemecode_idx",
        table_name="scheme_master",
        columns=["schemecode"],
        unique=True,
    )
    op.create_index(
        index_name="scheme_master_amc_code_idx",
        table_name="scheme_master",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_master_scheme_name_idx",
        table_name="scheme_master",
        columns=["scheme_name"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_master")
