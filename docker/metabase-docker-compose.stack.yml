version: "3.8"

services:
  metabase:
    image: metabase/metabase:latest
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
    env_file:
      - ./../../files/data/prod.env
    networks:
      - metabase-network
    ports:
      - "0.0.0.0:3002:3000"
    volumes:
      - /mnt/metabase_volume:/metabase-data
    hostname: metabase

networks:
  dokploy-network:
    external: true
  metabase-network:
    external: true

volumes:
  metabase-data:
