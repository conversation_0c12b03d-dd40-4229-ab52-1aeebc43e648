"""amc_keypersons

Revision ID: 3d74867ada37
Revises: bfafe89c0a56
Create Date: 2025-03-09 15:56:48.396272

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "3d74867ada37"
down_revision: Union[str, None] = "bfafe89c0a56"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "amc_keypersons",
        sa.Column(
            "amc_code",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column("amc_name", sa.Text(), nullable=True),  # nvarchar(max) -> Text
        sa.Column("srno", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("name", sa.String(length=1000), nullable=True),
        sa.Column("desig", sa.String(length=1000), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.<PERSON>KeyConstraint("amc_code", "srno"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="amc_keypersons_amc_code_idx",
        table_name="amc_keypersons",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("amc_keypersons")
