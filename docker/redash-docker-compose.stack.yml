version: "3.8"

services:
  redash_server:
    image: redash/redash:25.1.0
    command: server
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.service == redash
          - node.id == fiuytla48hbq2cw0ojaemmp9q
    env_file:
      - ./../../files/data/prod.env
    environment:
      - QUEUES=queries,scheduled_queries,schemas,default,periodic,celery
      - REDASH_HOST=0.0.0.0
    networks:
      - dokploy-network
    ports:
      - "0.0.0.0:5000:5000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://0.0.0.0:5000/status"]
      interval: 20s
      timeout: 90s
      retries: 5

  redash_scheduler:
    image: redash/redash:25.1.0
    command: scheduler
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.service == redash
          - node.id == fiuytla48hbq2cw0ojaemmp9q
    env_file:
      - ./../../files/data/prod.env
    environment:
      - QUEUES=queries,scheduled_queries,schemas,default,periodic,celery
    networks:
      - dokploy-network

  redash_worker:
    image: redash/redash:25.1.0
    command: worker
    deploy:
      replicas: 4 # Scale workers as needed
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.labels.service == redash
          - node.id == fiuytla48hbq2cw0ojaemmp9q

    env_file:
      - ./../../files/data/prod.env
    environment:
      - QUEUES=queries,scheduled_queries,schemas,default,periodic,celery
    networks:
      - dokploy-network

networks:
  dokploy-network:
    external: true
