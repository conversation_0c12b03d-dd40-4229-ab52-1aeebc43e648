"""create_scheme_rgress

Revision ID: 155977297680
Revises: b1fb7bec1e11
Create Date: 2025-03-09 17:32:57.684742

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "155977297680"
down_revision: Union[str, None] = "b1fb7bec1e11"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_rgess",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            primary_key=True,
            nullable=False,
        ),
        sa.Column("schemename", sa.String(length=255), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_rgess_schemecode_idx",
        table_name="scheme_rgess",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_rgess")
