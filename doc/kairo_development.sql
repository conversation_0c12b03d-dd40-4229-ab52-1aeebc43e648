-- -------------------------------------------------------------
-- TablePlus 6.6.5(627)
--
-- https://tableplus.com/
--
-- Database: kairo_development
-- Generation Time: 2025-07-09 14:16:33.5070
-- -------------------------------------------------------------


-- Table Definition
CREATE TABLE "public"."amc_mst_new" (
    "amc_code" int8 NOT NULL,
    "amc" varchar(255),
    "fund" varchar(255),
    "srno" int8,
    "office_type" varchar(60),
    "add1" text,
    "add2" text,
    "add3" text,
    "email" varchar(255),
    "phone" varchar(255),
    "fax" varchar(255),
    "webiste" varchar(255),
    "setup_date" timestamp,
    "mf_type" varchar(255),
    "trustee_name" varchar(255),
    "sponsor_name" text,
    "amc_inc_date" timestamp,
    "s_name" varchar(50),
    "amc_symbol" varchar(50),
    "city" varchar(255),
    "rtamccode" varchar(100),
    "rtamccode_1" varchar(100),
    "flag" varchar(1),
    PRIMARY KEY ("amc_code")
);

-- Table Definition
CREATE TABLE "public"."amc_keypersons" (
    "amc_code" int8 NOT NULL,
    "amc_name" text,
    "srno" int8 NOT NULL,
    "name" varchar(1000),
    "desig" varchar(1000),
    "flag" varchar(1),
    PRIMARY KEY ("amc_code","srno")
);

-- Table Definition
CREATE TABLE "public"."scheme_master" (
    "schemecode" int8 NOT NULL,
    "amc_code" int8,
    "scheme_name" varchar(255),
    "color" varchar(50),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS scheme_details_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."scheme_details" (
    "schemecode" int8 NOT NULL DEFAULT nextval('scheme_details_schemecode_seq'::regclass),
    "amfi_code" int8,
    "cams_code" varchar(50),
    "amc_code" int8,
    "s_name" varchar(255),
    "amfi_name" varchar(500),
    "isin_code" varchar(50),
    "type_code" int4,
    "opt_code" int4,
    "classcode" int4,
    "theme_code" int4,
    "rt_code" int4,
    "plan" int4,
    "cust_code" int4,
    "cust_code2" int4,
    "price_freq" int4,
    "init_price" numeric(18,6),
    "offerprice" numeric(18,6),
    "nfo_open" timestamp,
    "nfo_close" timestamp,
    "reopen_dt" timestamp,
    "elf" varchar(1),
    "etf" varchar(1),
    "flag" varchar(1),
    "stp" varchar(1),
    "primary_fund" varchar(1),
    "primary_fd_code" int4,
    "sip" varchar(1),
    "swp" varchar(1),
    "switch" varchar(1),
    "mininvt" numeric(18,6),
    "multiples" int4,
    "inc_invest" numeric(18,6),
    "adnmultiples" numeric(18,6),
    "fund_mgr1" varchar(1000),
    "fund_mgr2" varchar(1000),
    "fund_mgr3" varchar(1000),
    "fund_mgr4" varchar(1000),
    "since" timestamp,
    "status" varchar(50),
    "cutsub" varchar(10),
    "cutred" varchar(10),
    "red" varchar(50),
    "mob_name" varchar(255),
    "div_freq" int4,
    "scheme_symbol" varchar(50),
    "fund_mgr_code1" int4,
    "fund_mgr_code2" int4,
    "fund_mgr_code3" int4,
    "fund_mgr_code4" int4,
    "redemption_date" timestamp,
    "dateofallot" timestamp,
    "div_code" float8,
    "legalnames" varchar(255),
    "amfitype" varchar(50),
    "nontxnday" varchar(4),
    "schemebank" varchar(255),
    "schemebankaccountnumber" varchar(50),
    "schemebankbranch" varchar(255),
    "dividendoptionflag" varchar(1),
    "lockin" varchar(50),
    "ispurchaseavailable" varchar(1),
    "isredeemavailable" varchar(1),
    "minredemptionamount" float8,
    "redemptionmultipleamount" float8,
    "minredemptionunits" float8,
    "redemptionmultiplesunits" float8,
    "minswitchamount" float8,
    "switchmultipleamount" float8,
    "minswitchunits" float8,
    "switchmultiplesunits" float8,
    "securitycode" varchar(50),
    "unit" varchar(50),
    "switchout" varchar(1),
    "minswitchoutamount" float8,
    "switchoutmultipleamount" float8,
    "minswitchoutunits" float8,
    "switchoutmultiplesunits" float8,
    "incept_date" timestamp,
    "incept_nav" numeric(18,6),
    "defaultopt" varchar(50),
    "defaultplan" varchar(50),
    "lockperiod" int4,
    "oddraftdate" timestamp,
    "liquidated_date" timestamp,
    "old_plan" int4,
    "direct_plan" int4,
    "optiontype" varchar(10),
    PRIMARY KEY ("schemecode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS scheme_rtcode_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."scheme_rtcode" (
    "schemecode" int8 NOT NULL DEFAULT nextval('scheme_rtcode_schemecode_seq'::regclass),
    "rtschemecode" varchar(100),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Table Definition
CREATE TABLE "public"."schemeisinmaster" (
    "id" int8,
    "isin" varchar(100) NOT NULL,
    "schemecode" int8,
    "amc_code" int8,
    "nsesymbol" varchar(100),
    "series" varchar(50),
    "rtaschemecode" varchar(50),
    "flag" varchar(1),
    "amcschemecode" varchar(50),
    "longschemedescrip" varchar(255),
    "shortschemedescrip" varchar(255),
    "status" varchar(10),
    PRIMARY KEY ("isin")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS type_mst_type_code_seq;

-- Table Definition
CREATE TABLE "public"."type_mst" (
    "type_code" int8 NOT NULL DEFAULT nextval('type_mst_type_code_seq'::regclass),
    "type" varchar(50),
    "flag" varchar(1),
    PRIMARY KEY ("type_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS option_mst_opt_code_seq;

-- Table Definition
CREATE TABLE "public"."option_mst" (
    "opt_code" int8 NOT NULL DEFAULT nextval('option_mst_opt_code_seq'::regclass),
    "option" varchar(30),
    "flag" varchar(1),
    PRIMARY KEY ("opt_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS sclass_mst_classcode_seq;

-- Table Definition
CREATE TABLE "public"."sclass_mst" (
    "classcode" int8 NOT NULL DEFAULT nextval('sclass_mst_classcode_seq'::regclass),
    "classname" varchar(500),
    "asset_code" int8,
    "asset_type" varchar(500),
    "category" varchar(500),
    "sub_category" varchar(500),
    "flag" varchar(1),
    PRIMARY KEY ("classcode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS rt_mst_rt_code_seq;

-- Table Definition
CREATE TABLE "public"."rt_mst" (
    "rt_code" int8 NOT NULL DEFAULT nextval('rt_mst_rt_code_seq'::regclass),
    "rt_name" varchar(100),
    "sebi_reg_no" varchar(50),
    "address1" varchar(4000),
    "address2" varchar(4000),
    "address3" varchar(4000),
    "state" varchar(50),
    "tel" varchar(4000),
    "fax" varchar(4000),
    "website" varchar(100),
    "reg_address" varchar(4000),
    "email" varchar(500),
    "flag" varchar(1),
    PRIMARY KEY ("rt_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS plan_mst_plan_code_seq;

-- Table Definition
CREATE TABLE "public"."plan_mst" (
    "plan_code" int8 NOT NULL DEFAULT nextval('plan_mst_plan_code_seq'::regclass),
    "plan" varchar(50),
    "flag" varchar(1),
    PRIMARY KEY ("plan_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS cust_mst_cust_code_seq;

-- Table Definition
CREATE TABLE "public"."cust_mst" (
    "cust_code" int4 NOT NULL DEFAULT nextval('cust_mst_cust_code_seq'::regclass),
    "cust_name" varchar(100),
    "sebi_reg_no" varchar(25),
    "add1" text,
    "add2" text,
    "add3" text,
    "flag" varchar(1),
    PRIMARY KEY ("cust_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS fundmanager_mst_id_seq;

-- Table Definition
CREATE TABLE "public"."fundmanager_mst" (
    "id" int8 NOT NULL DEFAULT nextval('fundmanager_mst_id_seq'::regclass),
    "initial" varchar(10),
    "fundmanager" varchar(200),
    "qualification" varchar(200),
    "experience" varchar(200),
    "basicdetails" varchar(1000),
    "designation" varchar(100),
    "age" int4,
    "reporteddate" timestamp,
    "flag" varchar(1),
    PRIMARY KEY ("id")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS div_mst_div_code_seq;

-- Table Definition
CREATE TABLE "public"."div_mst" (
    "div_code" int4 NOT NULL DEFAULT nextval('div_mst_div_code_seq'::regclass),
    "div_type" varchar(30),
    "flag" varchar(1),
    PRIMARY KEY ("div_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS scheme_objective_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."scheme_objective" (
    "schemecode" int8 NOT NULL DEFAULT nextval('scheme_objective_schemecode_seq'::regclass),
    "objective" text,
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Table Definition
CREATE TABLE "public"."mf_sip" (
    "schemecode" int8 NOT NULL,
    "amc_code" int8,
    "frequency" varchar(100) NOT NULL,
    "sip" varchar(1),
    "sipdatescondition" varchar(8000),
    "dates" varchar(8000),
    "sipdaysall" varchar(50),
    "sipmininvest" numeric(18,6),
    "sipaddninvest" numeric(18,6),
    "sipfrequencyno" int4,
    "sipminimumperiod" int4,
    "sipmaximumperiod" varchar(100),
    "sipmincumamount" varchar(100),
    "sipminunits" numeric(18,6),
    "sipmultiplesunits" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","frequency")
);

-- Table Definition
CREATE TABLE "public"."mf_swp" (
    "schemecode" int8 NOT NULL,
    "amc_code" int8,
    "frequency" varchar(100) NOT NULL,
    "swp" varchar(1),
    "swpdatescondition" varchar(8000),
    "dates" varchar(8000),
    "swpdaysall" varchar(50),
    "swpmininvest" numeric(18,6),
    "swpaddninvest" numeric(18,6),
    "swpfrequencyno" int4,
    "swpminimumperiod" int4,
    "swpmaximumperiod" varchar(100),
    "swpmincumamount" varchar(100),
    "swpminunits" numeric(18,6),
    "swpmultiplesunits" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","frequency")
);

-- Table Definition
CREATE TABLE "public"."mf_stp" (
    "schemecode" int8 NOT NULL,
    "amc_code" int8,
    "frequency" varchar(100) NOT NULL,
    "stpinout" varchar(1) NOT NULL,
    "stp" varchar(1),
    "stpdatescondition" varchar(8000),
    "dates" varchar(8000),
    "stpdaysall" varchar(50),
    "stpmininvest" numeric(18,6),
    "stpaddninvest" numeric(18,6),
    "stpfrequencyno" int4,
    "stpminimumperiod" int4,
    "stpmaximumperiod" varchar(100),
    "stpmincumamount" varchar(100),
    "stpminunits" numeric(18,6),
    "stpmultiplesunits" numeric(18,6),
    "flag" varchar(100),
    PRIMARY KEY ("schemecode","frequency","stpinout")
);

-- Table Definition
CREATE TABLE "public"."scheme_index_part" (
    "schemecode" int8 NOT NULL,
    "indexcode" int8 NOT NULL,
    "benchmark_weightage" numeric(18,6),
    "indexorder" int4,
    "remark" varchar(100),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","indexcode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS index_mst_indexcode_seq;

-- Table Definition
CREATE TABLE "public"."index_mst" (
    "indexcode" int8 NOT NULL DEFAULT nextval('index_mst_indexcode_seq'::regclass),
    "fincode" int8,
    "scripcode" int8,
    "indexname" varchar(255),
    "index_gp" varchar(250),
    "subgroup" varchar(250),
    "flag" varchar(1),
    PRIMARY KEY ("indexcode")
);

-- Table Definition
CREATE TABLE "public"."schemeload" (
    "schemecode" int8 NOT NULL,
    "ldate" timestamp NOT NULL,
    "ltypecode" int8 NOT NULL,
    "lsrno" int8 NOT NULL,
    "frmamount" numeric(18,6),
    "uptoamount" numeric(18,6),
    "minperiod" int4,
    "maxperiod" int4,
    "min" varchar(10),
    "max" varchar(10),
    "entryload" numeric(18,6),
    "exitload" numeric(18,6),
    "remarks" text,
    "period_condition" varchar(10),
    "period_type" varchar(10),
    "period" varchar(100),
    "amount_condition" varchar(10),
    "amount_type" varchar(10),
    "per_condition" varchar(10),
    "per_frm" numeric(18,6),
    "per_to" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","ldate","ltypecode","lsrno")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS loadtype_mst_ltypecode_seq;

-- Table Definition
CREATE TABLE "public"."loadtype_mst" (
    "ltypecode" int8 NOT NULL DEFAULT nextval('loadtype_mst_ltypecode_seq'::regclass),
    "load" varchar(20),
    "flag" varchar(1),
    PRIMARY KEY ("ltypecode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS companymaster_fincode_seq;

-- Table Definition
CREATE TABLE "public"."companymaster" (
    "fincode" int8 NOT NULL DEFAULT nextval('companymaster_fincode_seq'::regclass),
    "scripcode" int8,
    "symbol" varchar(50),
    "compname" varchar(255),
    "s_name" varchar(100),
    "ind_code" int4,
    "industry" varchar(100),
    "isin" varchar(50),
    "status" varchar(50),
    "series" varchar(2),
    "listing" varchar(50),
    "sublisting" varchar(50),
    "fv" float8,
    "flag" varchar(1),
    PRIMARY KEY ("fincode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS industry_mst_ind_code_seq;

-- Table Definition
CREATE TABLE "public"."industry_mst" (
    "ind_code" int8 NOT NULL DEFAULT nextval('industry_mst_ind_code_seq'::regclass),
    "industry" varchar(255),
    "ind_shortname" varchar(255),
    "sector" varchar(255),
    "sector_code" int4,
    "flag" varchar(1),
    PRIMARY KEY ("ind_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS asect_mst_asect_code_seq;

-- Table Definition
CREATE TABLE "public"."asect_mst" (
    "asect_code" int4 NOT NULL DEFAULT nextval('asect_mst_asect_code_seq'::regclass),
    "asect_type" varchar(100),
    "asset" varchar(50),
    "as_name" varchar(50),
    "flag" varchar(1),
    PRIMARY KEY ("asect_code")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS scheme_rgess_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."scheme_rgess" (
    "schemecode" int8 NOT NULL DEFAULT nextval('scheme_rgess_schemecode_seq'::regclass),
    "schemename" varchar(255),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Table Definition
CREATE TABLE "public"."mf_portfolio" (
    "schemecode" int8 NOT NULL,
    "invdate" timestamp NOT NULL,
    "invenddate" timestamp,
    "srno" int8 NOT NULL,
    "fincode" int8,
    "asect_code" int8,
    "sect_code" int8,
    "noshares" numeric(18,0),
    "mktval" numeric(18,6),
    "aum" numeric(18,6),
    "holdpercentage" numeric(18,6),
    "compname" varchar(255),
    "sect_name" varchar(50),
    "asect_name" varchar(50),
    "rating" varchar(50),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","invdate","srno")
);

-- Table Definition
CREATE TABLE "public"."amc_paum" (
    "amc_code" int8 NOT NULL,
    "aumdate" timestamp NOT NULL,
    "totalaum" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("amc_code","aumdate")
);

-- Table Definition
CREATE TABLE "public"."scheme_paum" (
    "schemecode" int8 NOT NULL,
    "monthend" int4 NOT NULL,
    "amc_code" int8,
    "aum" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","monthend")
);

-- Table Definition
CREATE TABLE "public"."amc_aum" (
    "amc_code" int8 NOT NULL,
    "aumdate" timestamp NOT NULL,
    "totalaum" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("amc_code","aumdate")
);

-- Table Definition
CREATE TABLE "public"."scheme_aum" (
    "schemecode" int8 NOT NULL,
    "date" timestamp NOT NULL,
    "exfof" float8,
    "fof" float8,
    "total" float8,
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","date")
);

-- Table Definition
CREATE TABLE "public"."portfolio_inout" (
    "fincode" int8 NOT NULL,
    "invdate" timestamp NOT NULL,
    "mode" varchar(5) NOT NULL,
    "compname" varchar(255),
    "s_name" varchar(150),
    "mktval" numeric(18,6),
    "noshares" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("fincode","invdate","mode")
);

-- Table Definition
CREATE TABLE "public"."avg_scheme_aum" (
    "schemecode" int8 NOT NULL,
    "date" timestamp NOT NULL,
    "exfof" numeric(18,6),
    "fof" numeric(18,6),
    "total" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","date")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS currentnav_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."currentnav" (
    "schemecode" int8 NOT NULL DEFAULT nextval('currentnav_schemecode_seq'::regclass),
    "navdate" timestamp,
    "navrs" numeric(18,6),
    "repurprice" numeric(18,6),
    "saleprice" numeric(18,6),
    "cldate" timestamp,
    "change" numeric(18,6),
    "netchange" numeric(18,6),
    "prevnav" numeric(18,6),
    "prenavdate" timestamp,
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Table Definition
CREATE TABLE "public"."navhist" (
    "schemecode" int8 NOT NULL,
    "navdate" timestamp NOT NULL,
    "navrs" numeric(18,6),
    "repurprice" numeric(18,6),
    "saleprice" numeric(18,6),
    "adjustednav_c" numeric(18,6),
    "adjustednav_nonc" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","navdate")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS navhist_hl_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."navhist_hl" (
    "schemecode" int8 NOT NULL DEFAULT nextval('navhist_hl_schemecode_seq'::regclass),
    "3monthhhigh" numeric(18,6),
    "3monthlow" numeric(18,6),
    "3mhdate" timestamp,
    "3mldate" timestamp,
    "6monthhhigh" numeric(18,6),
    "6monthlow" numeric(18,6),
    "6mhdate" timestamp,
    "6mldate" timestamp,
    "9monthhhigh" numeric(18,6),
    "9monthlow" numeric(18,6),
    "9mhdate" timestamp,
    "9mldate" timestamp,
    "52weekhhigh" numeric(18,6),
    "52weeklow" numeric(18,6),
    "52whdate" timestamp,
    "52wldate" timestamp,
    "1yrhigh" numeric(18,6),
    "1yrlow" numeric(18,6),
    "1yhdate" timestamp,
    "1yldate" timestamp,
    "2yrhigh" numeric(18,6),
    "2yrlow" numeric(18,6),
    "2yhdate" timestamp,
    "2yldate" timestamp,
    "3yrhigh" numeric(18,6),
    "3yrlow" numeric(18,6),
    "3yhdate" timestamp,
    "3yldate" timestamp,
    "5yrhigh" numeric(18,6),
    "5yrlow" numeric(18,6),
    "5yhdate" timestamp,
    "5yldate" timestamp,
    "ytdhigh" numeric(18,6),
    "ytdlow" numeric(18,6),
    "ytdhdate" timestamp,
    "ytdldate" timestamp,
    "sihigh" numeric(18,6),
    "silow" numeric(18,6),
    "sihdate" timestamp,
    "sildate" timestamp,
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS avg_maturity_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."avg_maturity" (
    "amc_code" int8,
    "schemecode" int8 NOT NULL DEFAULT nextval('avg_maturity_schemecode_seq'::regclass),
    "date" timestamp,
    "invenddate" timestamp,
    "avg_mat_num" numeric(19,6),
    "avg_mat_days" varchar(25),
    "mod_dur_num" numeric(19,6),
    "mod_dur_days" varchar(25),
    "ytm" numeric(19,6),
    "turnover_ratio" numeric(19,6),
    "tr_mode" varchar(20),
    "flag" varchar(2),
    PRIMARY KEY ("schemecode")
);

-- Table Definition
CREATE TABLE "public"."mf_return" (
    "schemecode" numeric(18,6) NOT NULL,
    "c_date" timestamp,
    "p_date" timestamp,
    "c_nav" numeric(18,6),
    "p_nav" numeric(18,6),
    "1dayret" numeric(18,6),
    "1weekdate" timestamp,
    "1weeknav" numeric(18,6),
    "1weekret" numeric(18,6),
    "1mthdate" timestamp,
    "1mthnav" numeric(18,6),
    "1monthret" numeric(18,6),
    "3mthdate" timestamp,
    "3mthnav" numeric(18,6),
    "3monthret" numeric(18,6),
    "6mntdate" timestamp,
    "6mnthnav" numeric(18,6),
    "6monthret" numeric(18,6),
    "9mnthdate" timestamp,
    "9mnthnav" numeric(18,6),
    "9mnthret" numeric(18,6),
    "1yrdate" timestamp,
    "1yrnav" numeric(18,6),
    "1yrret" numeric(18,6),
    "2yrdate" timestamp,
    "2yrnav" numeric(18,6),
    "2yearret" numeric(18,6),
    "3yrdate" timestamp,
    "3yrnav" numeric(18,6),
    "3yearret" numeric(18,6),
    "4yearret" numeric(18,6),
    "4yrdate" timestamp,
    "4yrnav" numeric(18,6),
    "5yrdate" timestamp,
    "5yrnav" numeric(18,6),
    "5yearret" numeric(18,6),
    "flag" varchar(1),
    "incdate" timestamp,
    "incnav" numeric(18,6),
    "incret" numeric(18,6),
    PRIMARY KEY ("schemecode")
);

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS bm_annualisedreturn_index_code_seq;

-- Table Definition
CREATE TABLE "public"."bm_annualisedreturn" (
    "index_code" int8 NOT NULL DEFAULT nextval('bm_annualisedreturn_index_code_seq'::regclass),
    "symbol" varchar(255),
    "scripcode" int8,
    "date" timestamp,
    "prev_date" timestamp,
    "close" numeric(18,6),
    "prev_close" numeric(18,6),
    "1dayret" numeric(18,6),
    "1weekdate" timestamp,
    "1weekclose" numeric(18,6),
    "1weekret" numeric(18,6),
    "1mthdate" timestamp,
    "1mthclose" numeric(18,6),
    "1monthret" numeric(18,6),
    "3mthdate" timestamp,
    "3mthclose" numeric(18,6),
    "3monthret" numeric(18,6),
    "6mntdate" timestamp,
    "6mnthclose" numeric(18,6),
    "6monthret" numeric(18,6),
    "9mnthdate" timestamp,
    "9mnthclose" numeric(18,6),
    "9mnthret" numeric(18,6),
    "1yrdate" timestamp,
    "1yrclose" numeric(18,6),
    "1yrret" numeric(18,6),
    "2yrdate" timestamp,
    "2yrclose" numeric(18,6),
    "2yearret" numeric(18,6),
    "3yrdate" timestamp,
    "3yrclose" numeric(18,6),
    "3yearret" numeric(18,6),
    "4yrdate" timestamp,
    "4yrclose" numeric(18,6),
    "4yearret" numeric(18,6),
    "5yrdate" timestamp,
    "5yrclose" numeric(18,6),
    "5yearret" numeric(18,6),
    "incdate" timestamp,
    "incclose" numeric(18,6),
    "incret" numeric(18,6),
    "2weekdate" timestamp,
    "2weekclose" numeric(18,6),
    "2weekret" numeric(18,6),
    "3weekdate" timestamp,
    "3weekclose" numeric(18,6),
    "3weekret" numeric(18,6),
    "2mthdate" timestamp,
    "2mthclose" numeric(18,6),
    "2monthret" numeric(18,6),
    "ytddate" timestamp,
    "ytdclose" numeric(18,6),
    "ytdret" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("index_code")
);

-- Column Comment
COMMENT ON COLUMN "public"."bm_annualisedreturn"."index_code" IS 'Accord Fintech’s Index Code';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."symbol" IS 'NSE Symbol Code';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."scripcode" IS 'BSE Scrip code';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."date" IS 'Current Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."prev_date" IS 'Previous Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."close" IS 'Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."prev_close" IS 'Previous Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1dayret" IS 'One Day Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1weekdate" IS 'One Week Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1weekclose" IS 'One Week Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1weekret" IS 'One Week Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1mthdate" IS 'One Month Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1mthclose" IS 'One Month Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1monthret" IS 'One Month Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3mthdate" IS 'Three Month Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3mthclose" IS 'Three Month Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3monthret" IS 'Three Month Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."6mntdate" IS 'Six Month Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."6mnthclose" IS 'Six Month Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."6monthret" IS 'Six Month Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."9mnthdate" IS 'Nine Month Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."9mnthclose" IS 'Nine Month Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."9mnthret" IS 'Nine Month Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1yrdate" IS 'One Year Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1yrclose" IS 'One Year Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."1yrret" IS 'One Year Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2yrdate" IS 'Two Year Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2yrclose" IS 'Two Year Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2yearret" IS 'Two Year Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3yrdate" IS 'Three Year Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3yrclose" IS 'Three Year Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3yearret" IS 'Three Year Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."4yrdate" IS 'Four Year Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."4yrclose" IS 'Four Year Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."4yearret" IS 'Four Year Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."5yrdate" IS 'Five Year Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."5yrclose" IS 'Five Year Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."5yearret" IS 'Five Year Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."incdate" IS 'Inception date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."incclose" IS 'Close on inception date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."incret" IS 'Return on inception date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2weekdate" IS 'Two Week Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2weekclose" IS 'Two Week Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2weekret" IS 'Two Week Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3weekdate" IS 'Three Week Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3weekclose" IS 'Three Week Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."3weekret" IS 'Three Week Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2mthdate" IS 'Two Month Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2mthclose" IS 'Two Month Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."2monthret" IS 'Two Month Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."ytddate" IS 'YTD Date';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."ytdclose" IS 'YTD Close';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."ytdret" IS 'YTD Return';
COMMENT ON COLUMN "public"."bm_annualisedreturn"."flag" IS 'Updation Flag';

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS bm_absolutereturn_index_code_seq;

-- Table Definition
CREATE TABLE "public"."bm_absolutereturn" (
    "index_code" int8 NOT NULL DEFAULT nextval('bm_absolutereturn_index_code_seq'::regclass),
    "symbol" varchar(255),
    "scripcode" int4,
    "date" timestamp,
    "prev_date" timestamp,
    "close" numeric(18,6),
    "prev_close" numeric(18,6),
    "1dayret" numeric(18,6),
    "1weekdate" timestamp,
    "1weekclose" numeric(18,6),
    "1weekret" numeric(18,6),
    "1mthdate" timestamp,
    "1mthclose" numeric(18,6),
    "1monthret" numeric(18,6),
    "3mthdate" timestamp,
    "3mthclose" numeric(18,6),
    "3monthret" numeric(18,6),
    "6mntdate" timestamp,
    "6mnthclose" numeric(18,6),
    "6monthret" numeric(18,6),
    "9mnthdate" timestamp,
    "9mnthclose" numeric(18,6),
    "9mnthret" numeric(18,6),
    "1yrdate" timestamp,
    "1yrclose" numeric(18,6),
    "1yrret" numeric(18,6),
    "2yrdate" timestamp,
    "2yrclose" numeric(18,6),
    "2yearret" numeric(18,6),
    "3yrdate" timestamp,
    "3yrclose" numeric(18,6),
    "3yearret" numeric(18,6),
    "4yrdate" timestamp,
    "4yrclose" numeric(18,6),
    "4yearret" numeric(18,6),
    "5yrdate" timestamp,
    "5yrclose" numeric(18,6),
    "5yearret" numeric(18,6),
    "incdate" timestamp,
    "incclose" numeric(18,6),
    "incret" numeric(18,6),
    "2weekdate" timestamp,
    "2weekclose" numeric(18,6),
    "2weekret" numeric(18,6),
    "3weekdate" timestamp,
    "3weekclose" numeric(18,6),
    "3weekret" numeric(18,6),
    "2mthdate" timestamp,
    "2mthclose" numeric(18,6),
    "2monthret" numeric(18,6),
    "ytddate" timestamp,
    "ytdclose" numeric(18,6),
    "ytdret" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("index_code")
);

-- Column Comment
COMMENT ON COLUMN "public"."bm_absolutereturn"."index_code" IS 'Accord Fintech’s Index Code';
COMMENT ON COLUMN "public"."bm_absolutereturn"."symbol" IS 'NSE Symbol Code';
COMMENT ON COLUMN "public"."bm_absolutereturn"."scripcode" IS 'BSE Scrip code';
COMMENT ON COLUMN "public"."bm_absolutereturn"."date" IS 'Current Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."prev_date" IS 'Previous Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."close" IS 'Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."prev_close" IS 'Previous Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1dayret" IS 'One Day Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1weekdate" IS 'One Week Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1weekclose" IS 'One Week Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1weekret" IS 'One Week Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1mthdate" IS 'One Month Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1mthclose" IS 'One Month Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1monthret" IS 'One Month Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3mthdate" IS 'Three Month Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3mthclose" IS 'Three Month Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3monthret" IS 'Three Month Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."6mntdate" IS 'Six Month Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."6mnthclose" IS 'Six Month Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."6monthret" IS 'Six Month Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."9mnthdate" IS 'Nine Month Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."9mnthclose" IS 'Nine Month Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."9mnthret" IS 'Nine Month Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1yrdate" IS 'One Year Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1yrclose" IS 'One Year Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."1yrret" IS 'One Year Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2yrdate" IS 'Two Year Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2yrclose" IS 'Two Year Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2yearret" IS 'Two Year Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3yrdate" IS 'Three Year Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3yrclose" IS 'Three Year Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3yearret" IS 'Three Year Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."4yrdate" IS 'Four Year Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."4yrclose" IS 'Four Year Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."4yearret" IS 'Four Year Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."5yrdate" IS 'Five Year Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."5yrclose" IS 'Five Year Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."5yearret" IS 'Five Year Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."incdate" IS 'Inception date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."incclose" IS 'Close on inception date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."incret" IS 'Return on inception date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2weekdate" IS 'Two Week Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2weekclose" IS 'Two Week Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2weekret" IS 'Two Week Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3weekdate" IS 'Three Week Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3weekclose" IS 'Three Week Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."3weekret" IS 'Three Week Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2mthdate" IS 'Two Month Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2mthclose" IS 'Two Month Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."2monthret" IS 'Two Month Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."ytddate" IS 'YTD Date';
COMMENT ON COLUMN "public"."bm_absolutereturn"."ytdclose" IS 'YTD Close';
COMMENT ON COLUMN "public"."bm_absolutereturn"."ytdret" IS 'YTD Return';
COMMENT ON COLUMN "public"."bm_absolutereturn"."flag" IS 'Updation Flag';

-- Table Definition
CREATE TABLE "public"."classwisereturn" (
    "classcode" int8 NOT NULL,
    "classname" varchar(500),
    "opt_code" int8 NOT NULL,
    "date" timestamp,
    "1dayret" numeric(18,3),
    "1weekret" numeric(18,3),
    "2weekret" numeric(18,3),
    "3weekret" numeric(18,3),
    "1monthret" numeric(18,3),
    "2monthret" numeric(18,3),
    "3monthret" numeric(18,3),
    "6monthret" numeric(18,3),
    "9mnthret" numeric(18,3),
    "1yearret" numeric(18,3),
    "2yearret" numeric(18,3),
    "3yearret" numeric(18,3),
    "4yearret" numeric(18,3),
    "5yearret" numeric(18,3),
    "incret" numeric(18,3),
    "ytdret" numeric(18,3),
    "1wschemecode" int8,
    "weekhighret" numeric(18,3),
    "1mschemecode" int8,
    "monthhighret" numeric(18,3),
    "3mschemecode" int8,
    "3monthhighret" numeric(18,3),
    "6mschemecode" int8,
    "6monthhighret" numeric(18,3),
    "1yschemecode" int8,
    "1yhighret" numeric(18,3),
    "3yschemecode" int8,
    "3yhighret" numeric(18,3),
    "5yschemecode" int8,
    "5yhighret" numeric(18,3),
    "incretschemecode" int8,
    "increthighret" numeric(18,3),
    "worst1wschemecode" int8,
    "weekworstret" numeric(18,3),
    "worst1mschemecode" int8,
    "monthworstret" numeric(18,3),
    "worst3mschemecode" int8,
    "3monthworstret" numeric(18,3),
    "worst6mschemecode" int8,
    "6monthworstret" numeric(18,3),
    "worst1yschemecode" int8,
    "1yworstret" numeric(18,3),
    "worst3yschemecode" int8,
    "3yworstret" numeric(18,3),
    "worst5yschemecode" int8,
    "5yworstret" numeric(18,3),
    "worstincretschemecode" int8,
    "incretworstret" numeric(18,3),
    "flag" varchar(1),
    PRIMARY KEY ("classcode","opt_code")
);

-- Column Comment
COMMENT ON COLUMN "public"."classwisereturn"."classcode" IS 'Class Code (will take one of class codes of Category names from Sclass_mst table)';
COMMENT ON COLUMN "public"."classwisereturn"."classname" IS 'Category Name (will take Category names from Sclass_mst table)';
COMMENT ON COLUMN "public"."classwisereturn"."opt_code" IS 'Option Code';
COMMENT ON COLUMN "public"."classwisereturn"."date" IS 'Date';
COMMENT ON COLUMN "public"."classwisereturn"."1dayret" IS 'One Day Return';
COMMENT ON COLUMN "public"."classwisereturn"."1weekret" IS 'One Week Return';
COMMENT ON COLUMN "public"."classwisereturn"."2weekret" IS 'Two Week Return';
COMMENT ON COLUMN "public"."classwisereturn"."3weekret" IS 'Three Week Return';
COMMENT ON COLUMN "public"."classwisereturn"."1monthret" IS 'One Month Return';
COMMENT ON COLUMN "public"."classwisereturn"."2monthret" IS 'Two Month Return';
COMMENT ON COLUMN "public"."classwisereturn"."3monthret" IS 'Three Month Return';
COMMENT ON COLUMN "public"."classwisereturn"."6monthret" IS 'Six Month Return';
COMMENT ON COLUMN "public"."classwisereturn"."9mnthret" IS 'Nine Month Return';
COMMENT ON COLUMN "public"."classwisereturn"."1yearret" IS 'One Year Return';
COMMENT ON COLUMN "public"."classwisereturn"."2yearret" IS 'Two Year Return';
COMMENT ON COLUMN "public"."classwisereturn"."3yearret" IS 'Three Year Return';
COMMENT ON COLUMN "public"."classwisereturn"."4yearret" IS 'Four Year Return';
COMMENT ON COLUMN "public"."classwisereturn"."5yearret" IS 'Five Year Return';
COMMENT ON COLUMN "public"."classwisereturn"."incret" IS 'Return on inception date';
COMMENT ON COLUMN "public"."classwisereturn"."ytdret" IS 'YTD Return';
COMMENT ON COLUMN "public"."classwisereturn"."1wschemecode" IS '1 Week Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."weekhighret" IS 'Week High Return';
COMMENT ON COLUMN "public"."classwisereturn"."1mschemecode" IS '1 Month Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."monthhighret" IS 'Month High Return';
COMMENT ON COLUMN "public"."classwisereturn"."3mschemecode" IS '3 Month Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."3monthhighret" IS '3 Month High Return';
COMMENT ON COLUMN "public"."classwisereturn"."6mschemecode" IS '6 Month Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."6monthhighret" IS '6 Month High Return';
COMMENT ON COLUMN "public"."classwisereturn"."1yschemecode" IS '1 Year Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."1yhighret" IS '1 Year High Return';
COMMENT ON COLUMN "public"."classwisereturn"."3yschemecode" IS '3 Year Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."3yhighret" IS '3 Year High Return';
COMMENT ON COLUMN "public"."classwisereturn"."5yschemecode" IS '5 Year Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."5yhighret" IS '5 Year High Return';
COMMENT ON COLUMN "public"."classwisereturn"."incretschemecode" IS 'Inception Return Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."increthighret" IS 'Inception High Return';
COMMENT ON COLUMN "public"."classwisereturn"."worst1wschemecode" IS 'Worst 1week Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."weekworstret" IS 'Week Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."worst1mschemecode" IS 'Worst 1Month Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."monthworstret" IS 'Month Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."worst3mschemecode" IS 'Worst 3 Month Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."3monthworstret" IS '3 Month Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."worst6mschemecode" IS 'Worst 6 Month Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."6monthworstret" IS '6 Month Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."worst1yschemecode" IS 'Worst 1 Year Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."1yworstret" IS '1 Year Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."worst3yschemecode" IS 'Worst 3 Year Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."3yworstret" IS '3 Year Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."worst5yschemecode" IS 'Worst 5 Year Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."5yworstret" IS '5 Year Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."worstincretschemecode" IS 'Worst Inception Schemecode';
COMMENT ON COLUMN "public"."classwisereturn"."incretworstret" IS 'Inception Worst Return';
COMMENT ON COLUMN "public"."classwisereturn"."flag" IS 'Updation Flag';

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS companymcap_fincode_seq;

-- Table Definition
CREATE TABLE "public"."companymcap" (
    "fincode" int8 NOT NULL DEFAULT nextval('companymcap_fincode_seq'::regclass),
    "mcap" numeric(18,6),
    "mode" varchar(20),
    "flag" varchar(1),
    PRIMARY KEY ("fincode")
);

-- Column Comment
COMMENT ON COLUMN "public"."companymcap"."fincode" IS 'Accord company code';
COMMENT ON COLUMN "public"."companymcap"."mcap" IS 'Mcap';
COMMENT ON COLUMN "public"."companymcap"."mode" IS 'Mcap type';
COMMENT ON COLUMN "public"."companymcap"."flag" IS 'Updation Flag';

-- Table Definition
CREATE TABLE "public"."dailyfundmanager" (
    "date" timestamp NOT NULL,
    "amc" int8 NOT NULL,
    "schemecode" int8 NOT NULL,
    "fundmanger1" int8,
    "fundmanger2" int8,
    "fundmanger3" int8,
    "fundmanger4" int8,
    "flag" varchar(1),
    PRIMARY KEY ("date","amc","schemecode")
);

-- Column Comment
COMMENT ON COLUMN "public"."dailyfundmanager"."date" IS 'Date';
COMMENT ON COLUMN "public"."dailyfundmanager"."amc" IS 'Accord Fintech’s AMC Code is unique for each company';
COMMENT ON COLUMN "public"."dailyfundmanager"."schemecode" IS 'Accord Fintech’s Schemecode';
COMMENT ON COLUMN "public"."dailyfundmanager"."fundmanger1" IS 'fund manager1 code';
COMMENT ON COLUMN "public"."dailyfundmanager"."fundmanger2" IS 'fund manager2 code';
COMMENT ON COLUMN "public"."dailyfundmanager"."fundmanger3" IS 'fund manager3 code';
COMMENT ON COLUMN "public"."dailyfundmanager"."fundmanger4" IS 'fund manager4 code';
COMMENT ON COLUMN "public"."dailyfundmanager"."flag" IS 'Updation Flag';

-- Table Definition
CREATE TABLE "public"."divdetails" (
    "amc_code" int8,
    "schemecode" int8 NOT NULL,
    "recorddate" timestamp NOT NULL,
    "div_code" int8,
    "exdivdate" timestamp,
    "bonusrate1" numeric(18,6),
    "bonusrate2" numeric(18,6),
    "gross" numeric(18,6),
    "corporate" numeric(18,6),
    "noncorporate" numeric(18,6),
    "status" varchar(3),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","recorddate")
);

-- Column Comment
COMMENT ON COLUMN "public"."divdetails"."amc_code" IS 'AMC Code is unique for each company';
COMMENT ON COLUMN "public"."divdetails"."schemecode" IS 'Accord Fintech’s Scheme Code';
COMMENT ON COLUMN "public"."divdetails"."recorddate" IS 'Record date';
COMMENT ON COLUMN "public"."divdetails"."div_code" IS 'Dividend code which comes from div_mst';
COMMENT ON COLUMN "public"."divdetails"."exdivdate" IS 'Ex-dividend date';
COMMENT ON COLUMN "public"."divdetails"."bonusrate1" IS 'Bonus rate1';
COMMENT ON COLUMN "public"."divdetails"."bonusrate2" IS 'Bonus rate2';
COMMENT ON COLUMN "public"."divdetails"."gross" IS 'Dividend percentage';
COMMENT ON COLUMN "public"."divdetails"."corporate" IS 'Corporate dividend percentage';
COMMENT ON COLUMN "public"."divdetails"."noncorporate" IS 'Non Corporate Dividend Percentage';
COMMENT ON COLUMN "public"."divdetails"."status" IS 'Status. Indicates unit of dividend % or P/U value';
COMMENT ON COLUMN "public"."divdetails"."flag" IS 'Updation Flag';

-- Table Definition
CREATE TABLE "public"."expenceratio" (
    "amc_code" int8,
    "schemecode" int8 NOT NULL,
    "date" timestamp NOT NULL,
    "expratio" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","date")
);

-- Column Comment
COMMENT ON COLUMN "public"."expenceratio"."amc_code" IS 'Accord Fintech’s AMC Code is unique for each company';
COMMENT ON COLUMN "public"."expenceratio"."schemecode" IS 'Accord Fintech’s Schemecode';
COMMENT ON COLUMN "public"."expenceratio"."date" IS 'Date';
COMMENT ON COLUMN "public"."expenceratio"."expratio" IS 'Expense Ratio';
COMMENT ON COLUMN "public"."expenceratio"."flag" IS 'Updation Flag';

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS fmp_yielddetails_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."fmp_yielddetails" (
    "schemecode" int8 NOT NULL DEFAULT nextval('fmp_yielddetails_schemecode_seq'::regclass),
    "maturitydate" timestamp,
    "tenure_number" numeric(18,6),
    "tenure_option" varchar(10),
    "net_inticative_yield1" numeric(18,6),
    "net_inticative_yield2" numeric(18,6),
    "post_taxyield_ind1" numeric(18,6),
    "post_taxyield_ind2" numeric(18,6),
    "post_taxyield_nonind1" numeric(18,6),
    "post_taxyield_nonind2" numeric(18,6),
    "exit_load" varchar(30),
    "rollover" varchar(1),
    "maturitydate_after_rollover" timestamp,
    "tenure_no_rollover" numeric(18,6),
    "tenure_option_rollover" varchar(10),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Column Comment
COMMENT ON COLUMN "public"."fmp_yielddetails"."schemecode" IS 'Accord Fintech’s Schemecode';
COMMENT ON COLUMN "public"."fmp_yielddetails"."maturitydate" IS 'Maturity date of FMP schemes';
COMMENT ON COLUMN "public"."fmp_yielddetails"."tenure_number" IS 'Schemes tenures in Numbers';
COMMENT ON COLUMN "public"."fmp_yielddetails"."tenure_option" IS 'Schemes Tenure in';
COMMENT ON COLUMN "public"."fmp_yielddetails"."net_inticative_yield1" IS 'Not available';
COMMENT ON COLUMN "public"."fmp_yielddetails"."net_inticative_yield2" IS 'Not available';
COMMENT ON COLUMN "public"."fmp_yielddetails"."post_taxyield_ind1" IS 'Not available';
COMMENT ON COLUMN "public"."fmp_yielddetails"."post_taxyield_ind2" IS 'Not available';
COMMENT ON COLUMN "public"."fmp_yielddetails"."post_taxyield_nonind1" IS 'Not available';
COMMENT ON COLUMN "public"."fmp_yielddetails"."post_taxyield_nonind2" IS 'Not available';
COMMENT ON COLUMN "public"."fmp_yielddetails"."exit_load" IS 'Exit Load';
COMMENT ON COLUMN "public"."fmp_yielddetails"."rollover" IS 'Roll Over flag';
COMMENT ON COLUMN "public"."fmp_yielddetails"."maturitydate_after_rollover" IS 'MaturityDate After Rollover';
COMMENT ON COLUMN "public"."fmp_yielddetails"."tenure_no_rollover" IS 'Tenure No Rollover';
COMMENT ON COLUMN "public"."fmp_yielddetails"."tenure_option_rollover" IS 'Tenure Option Rollover';
COMMENT ON COLUMN "public"."fmp_yielddetails"."flag" IS 'Updation Flag';

-- Table Definition
CREATE TABLE "public"."fvchange" (
    "amc_code" int8,
    "schemecode" int8 NOT NULL,
    "scheme_name" varchar(255),
    "fvbefore" numeric(18,6),
    "fvafter" numeric(18,6),
    "fvdate" timestamp NOT NULL,
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","fvdate")
);

-- Column Comment
COMMENT ON COLUMN "public"."fvchange"."amc_code" IS 'Accord Fintech’s AMC Code is unique for each company';
COMMENT ON COLUMN "public"."fvchange"."schemecode" IS 'Accord Fintech’s Schemecode';
COMMENT ON COLUMN "public"."fvchange"."scheme_name" IS 'Scheme Name';
COMMENT ON COLUMN "public"."fvchange"."fvbefore" IS 'Face Value Before';
COMMENT ON COLUMN "public"."fvchange"."fvafter" IS 'Face Value After';
COMMENT ON COLUMN "public"."fvchange"."fvdate" IS 'Face Value Date';
COMMENT ON COLUMN "public"."fvchange"."flag" IS 'Updation Flag';

-- Table Definition
CREATE TABLE "public"."mergedschemes" (
    "schemecode" int8 NOT NULL,
    "mergedwith" int8 NOT NULL,
    "effect_date" timestamp,
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","mergedwith")
);

-- Column Comment
COMMENT ON COLUMN "public"."mergedschemes"."schemecode" IS 'Accord Fintech’s Schemecode';
COMMENT ON COLUMN "public"."mergedschemes"."mergedwith" IS 'Accord Fintech’s Schemecode';
COMMENT ON COLUMN "public"."mergedschemes"."effect_date" IS 'Effective Date';
COMMENT ON COLUMN "public"."mergedschemes"."flag" IS 'Updation Flag';

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS mf_abs_return_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."mf_abs_return" (
    "schemecode" int8 NOT NULL DEFAULT nextval('mf_abs_return_schemecode_seq'::regclass),
    "c_date" timestamp,
    "p_date" timestamp,
    "c_nav" numeric(18,6),
    "p_nav" numeric(18,6),
    "1dayret" numeric(18,6),
    "1weekdate" timestamp,
    "1weeknav" numeric(18,6),
    "1weekret" numeric(18,6),
    "1mthdate" timestamp,
    "1mthnav" numeric(18,6),
    "1monthret" numeric(18,6),
    "3mthdate" timestamp,
    "3mthnav" numeric(18,6),
    "3monthret" numeric(18,6),
    "6mntdate" timestamp,
    "6mnthnav" numeric(18,6),
    "6monthret" numeric(18,6),
    "9mnthdate" timestamp,
    "9mnthnav" numeric(18,6),
    "9mnthret" numeric(18,6),
    "1yrdate" timestamp,
    "1yrnav" numeric(18,6),
    "1yrret" numeric(18,6),
    "2yrdate" timestamp,
    "2yrnav" numeric(18,6),
    "2yearret" numeric(18,6),
    "3yrdate" timestamp,
    "3yrnav" numeric(18,6),
    "3yearret" numeric(18,6),
    "4yrdate" timestamp,
    "4yrnav" numeric(18,6),
    "4yearret" numeric(18,6),
    "5yrdate" timestamp,
    "5yrnav" numeric(18,6),
    "5yearret" numeric(18,6),
    "incdate" timestamp,
    "incnav" numeric(18,6),
    "incret" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Column Comment
COMMENT ON COLUMN "public"."mf_abs_return"."schemecode" IS 'Accord Fintech’s Scheme Code';
COMMENT ON COLUMN "public"."mf_abs_return"."c_date" IS 'Closing Date';
COMMENT ON COLUMN "public"."mf_abs_return"."p_date" IS 'Previous Date';
COMMENT ON COLUMN "public"."mf_abs_return"."c_nav" IS 'Closing Nav';
COMMENT ON COLUMN "public"."mf_abs_return"."p_nav" IS 'Previous Day Nav';
COMMENT ON COLUMN "public"."mf_abs_return"."1dayret" IS 'One Day Return';
COMMENT ON COLUMN "public"."mf_abs_return"."1weekdate" IS 'One Week Date';
COMMENT ON COLUMN "public"."mf_abs_return"."1weeknav" IS 'One Week NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."1weekret" IS 'One Week Return';
COMMENT ON COLUMN "public"."mf_abs_return"."1mthdate" IS 'One Month Date';
COMMENT ON COLUMN "public"."mf_abs_return"."1mthnav" IS 'One Month NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."1monthret" IS 'One Month Return';
COMMENT ON COLUMN "public"."mf_abs_return"."3mthdate" IS '3 Month Date';
COMMENT ON COLUMN "public"."mf_abs_return"."3mthnav" IS '3 Month NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."3monthret" IS '3 Month Return';
COMMENT ON COLUMN "public"."mf_abs_return"."6mntdate" IS 'Six Month Date';
COMMENT ON COLUMN "public"."mf_abs_return"."6mnthnav" IS 'Six Month NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."6monthret" IS 'Six Month Return';
COMMENT ON COLUMN "public"."mf_abs_return"."9mnthdate" IS 'Nine Month Date';
COMMENT ON COLUMN "public"."mf_abs_return"."9mnthnav" IS 'Nine Month NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."9mnthret" IS 'Nine Month Return';
COMMENT ON COLUMN "public"."mf_abs_return"."1yrdate" IS 'One Year Date';
COMMENT ON COLUMN "public"."mf_abs_return"."1yrnav" IS 'One Year NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."1yrret" IS 'One Year Return';
COMMENT ON COLUMN "public"."mf_abs_return"."2yrdate" IS '2 Year Date';
COMMENT ON COLUMN "public"."mf_abs_return"."2yrnav" IS '2 Year NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."2yearret" IS '2 Year Return';
COMMENT ON COLUMN "public"."mf_abs_return"."3yrdate" IS '3 Year Date';
COMMENT ON COLUMN "public"."mf_abs_return"."3yrnav" IS '3 Year NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."3yearret" IS '3 Year Return';
COMMENT ON COLUMN "public"."mf_abs_return"."4yrdate" IS '4 Year Date';
COMMENT ON COLUMN "public"."mf_abs_return"."4yrnav" IS '4 Year NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."4yearret" IS '4 Year Return';
COMMENT ON COLUMN "public"."mf_abs_return"."5yrdate" IS '5 Year Date';
COMMENT ON COLUMN "public"."mf_abs_return"."5yrnav" IS '5 Year NAV';
COMMENT ON COLUMN "public"."mf_abs_return"."5yearret" IS '5 Year Return';
COMMENT ON COLUMN "public"."mf_abs_return"."incdate" IS 'Inception date';
COMMENT ON COLUMN "public"."mf_abs_return"."incnav" IS 'NAV on inception date';
COMMENT ON COLUMN "public"."mf_abs_return"."incret" IS 'Return on inception date';
COMMENT ON COLUMN "public"."mf_abs_return"."flag" IS 'Updation Flag';

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS mf_ans_return_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."mf_ans_return" (
    "schemecode" int8 NOT NULL DEFAULT nextval('mf_ans_return_schemecode_seq'::regclass),
    "c_date" timestamp,
    "p_date" timestamp,
    "c_nav" numeric(18,6),
    "p_nav" numeric(18,6),
    "1dayret" numeric(18,6),
    "1weekdate" timestamp,
    "1weeknav" numeric(18,6),
    "1weekret" numeric(18,6),
    "1mthdate" timestamp,
    "1mthnav" numeric(18,6),
    "1monthret" numeric(18,6),
    "3mthdate" timestamp,
    "3mthnav" numeric(18,6),
    "3monthret" numeric(18,6),
    "6mntdate" timestamp,
    "6mnthnav" numeric(18,6),
    "6monthret" numeric(18,6),
    "9mnthdate" timestamp,
    "9mnthnav" numeric(18,6),
    "9mnthret" numeric(18,6),
    "1yrdate" timestamp,
    "1yrnav" numeric(18,6),
    "1yrret" numeric(18,6),
    "2yrdate" timestamp,
    "2yrnav" numeric(18,6),
    "2yearret" numeric(18,6),
    "3yrdate" timestamp,
    "3yrnav" numeric(18,6),
    "3yearret" numeric(18,6),
    "4yrdate" timestamp,
    "4yrnav" numeric(18,6),
    "4yearret" numeric(18,6),
    "5yrdate" timestamp,
    "5yrnav" numeric(18,6),
    "5yearret" numeric(18,6),
    "incdate" timestamp,
    "incnav" numeric(18,6),
    "incret" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Column Comment
COMMENT ON COLUMN "public"."mf_ans_return"."schemecode" IS 'Accord Fintech’s Scheme Code';
COMMENT ON COLUMN "public"."mf_ans_return"."c_date" IS 'Closing Date';
COMMENT ON COLUMN "public"."mf_ans_return"."p_date" IS 'Previous Date';
COMMENT ON COLUMN "public"."mf_ans_return"."c_nav" IS 'Closing Nav';
COMMENT ON COLUMN "public"."mf_ans_return"."p_nav" IS 'Previous Day Nav';
COMMENT ON COLUMN "public"."mf_ans_return"."1dayret" IS 'One Day Return';
COMMENT ON COLUMN "public"."mf_ans_return"."1weekdate" IS 'One Week Date';
COMMENT ON COLUMN "public"."mf_ans_return"."1weeknav" IS 'One Week NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."1weekret" IS 'One Week Return';
COMMENT ON COLUMN "public"."mf_ans_return"."1mthdate" IS 'One Month Date';
COMMENT ON COLUMN "public"."mf_ans_return"."1mthnav" IS 'One Month NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."1monthret" IS 'One Month Return';
COMMENT ON COLUMN "public"."mf_ans_return"."3mthdate" IS 'Three Month Date';
COMMENT ON COLUMN "public"."mf_ans_return"."3mthnav" IS 'Three Month NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."3monthret" IS 'Three Month Return';
COMMENT ON COLUMN "public"."mf_ans_return"."6mntdate" IS 'Six Month Date';
COMMENT ON COLUMN "public"."mf_ans_return"."6mnthnav" IS 'Six Month NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."6monthret" IS 'Six Month Return';
COMMENT ON COLUMN "public"."mf_ans_return"."9mnthdate" IS 'Nine Month Date';
COMMENT ON COLUMN "public"."mf_ans_return"."9mnthnav" IS 'Nine Month NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."9mnthret" IS 'Nine Month Return';
COMMENT ON COLUMN "public"."mf_ans_return"."1yrdate" IS 'One Year Date';
COMMENT ON COLUMN "public"."mf_ans_return"."1yrnav" IS 'One Year NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."1yrret" IS 'One Year Return';
COMMENT ON COLUMN "public"."mf_ans_return"."2yrdate" IS 'Two Year Date';
COMMENT ON COLUMN "public"."mf_ans_return"."2yrnav" IS 'Two Year NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."2yearret" IS 'Two Year Return';
COMMENT ON COLUMN "public"."mf_ans_return"."3yrdate" IS 'Three Year Date';
COMMENT ON COLUMN "public"."mf_ans_return"."3yrnav" IS 'Three Year NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."3yearret" IS 'Three Year Return';
COMMENT ON COLUMN "public"."mf_ans_return"."4yrdate" IS 'Four Year Date';
COMMENT ON COLUMN "public"."mf_ans_return"."4yrnav" IS 'Four Year NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."4yearret" IS 'Four Year Return';
COMMENT ON COLUMN "public"."mf_ans_return"."5yrdate" IS 'Five Year Date';
COMMENT ON COLUMN "public"."mf_ans_return"."5yrnav" IS 'Five Year NAV';
COMMENT ON COLUMN "public"."mf_ans_return"."5yearret" IS 'Five Year Return';
COMMENT ON COLUMN "public"."mf_ans_return"."incdate" IS 'Inception date';
COMMENT ON COLUMN "public"."mf_ans_return"."incnav" IS 'NAV on inception date';
COMMENT ON COLUMN "public"."mf_ans_return"."incret" IS 'Return on inception date';
COMMENT ON COLUMN "public"."mf_ans_return"."flag" IS 'Updation flag';

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS mf_ratio_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."mf_ratio" (
    "schemecode" int8 NOT NULL DEFAULT nextval('mf_ratio_schemecode_seq'::regclass),
    "upddate" timestamp,
    "datefrom" timestamp,
    "dateto" timestamp,
    "avg_x" numeric(18,6),
    "avg_y" numeric(18,6),
    "sd_x" numeric(18,6),
    "sd_y" numeric(18,6),
    "jalpha_x" numeric(18,6),
    "jalpha_y" numeric(18,6),
    "semisd_x" numeric(18,6),
    "semisd_y" numeric(18,6),
    "beta_x" numeric(18,6),
    "beta_y" numeric(18,6),
    "corelation_x" numeric(18,6),
    "corelation_y" numeric(18,6),
    "betacor_x" numeric(18,6),
    "betacor_y" numeric(18,6),
    "treynor_x" numeric(18,6),
    "treynor_y" numeric(18,6),
    "fama_x" numeric(18,6),
    "fama_y" numeric(18,6),
    "sharpe_x" numeric(18,6),
    "sharpe_y" numeric(18,6),
    "sortino_x" numeric(18,6),
    "sortino_y" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode")
);

-- Column Comment
COMMENT ON COLUMN "public"."mf_ratio"."schemecode" IS 'Accord Fintech’s Scheme Code';
COMMENT ON COLUMN "public"."mf_ratio"."upddate" IS 'Updation Date';
COMMENT ON COLUMN "public"."mf_ratio"."datefrom" IS 'Ratio calculation ‘from’ date range';
COMMENT ON COLUMN "public"."mf_ratio"."dateto" IS 'Ratio calculation ‘to’ date range';
COMMENT ON COLUMN "public"."mf_ratio"."avg_x" IS 'Average Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."avg_y" IS 'Average Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."sd_x" IS 'Standard Deviation of the benchmark';
COMMENT ON COLUMN "public"."mf_ratio"."sd_y" IS 'Standard Deviation of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."jalpha_x" IS 'Jensen''s Alpha of the benchmark';
COMMENT ON COLUMN "public"."mf_ratio"."jalpha_y" IS 'Jensen''s Alpha of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."semisd_x" IS 'Semi Standard Deviation of the benchmark';
COMMENT ON COLUMN "public"."mf_ratio"."semisd_y" IS 'Semi Standard Deviation of Scheme';
COMMENT ON COLUMN "public"."mf_ratio"."beta_x" IS 'Beta of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."beta_y" IS 'Beta of the Scheme';
COMMENT ON COLUMN "public"."mf_ratio"."corelation_x" IS 'Corelation of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."corelation_y" IS 'Corelation of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."betacor_x" IS 'Beta Corelation of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."betacor_y" IS 'Beta Corelation of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."treynor_x" IS 'Treynor Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."treynor_y" IS 'Treynor Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."fama_x" IS 'FAMA Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."fama_y" IS 'FAMA Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."sharpe_x" IS 'Sharpe Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."sharpe_y" IS 'Sharpe Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."sortino_x" IS 'Sortino Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratio"."sortino_y" IS 'Sortino Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratio"."flag" IS 'Updation Flag';

-- Sequence and defined type
CREATE SEQUENCE IF NOT EXISTS mf_ratios_defaultbm_schemecode_seq;

-- Table Definition
CREATE TABLE "public"."mf_ratios_defaultbm" (
    "schemecode" int8 NOT NULL DEFAULT nextval('mf_ratios_defaultbm_schemecode_seq'::regclass),
    "upddate" timestamp,
    "datefrom" timestamp,
    "dateto" timestamp,
    "average" numeric(18,6),
    "bmaverage" numeric(18,6),
    "sd" numeric(18,6),
    "bmsd" numeric(18,6),
    "semisd" numeric(18,6),
    "semisdii" numeric(18,6),
    "beta" numeric(18,6),
    "correlation" numeric(18,6),
    "beta_corelation" numeric(18,6),
    "covariance" numeric(18,6),
    "treynor" numeric(18,6),
    "fama" numeric(18,6),
    "sharpe" numeric(18,6),
    "sharpe_x" numeric(18,6),
    "sharpe_y" numeric(18,6),
    "jalpha_x" numeric(18,6),
    "jalpha_y" numeric(18,6),
    "sortino_x" numeric(18,6),
    "sortino_y" numeric(18,6),
    "flag" varchar(1),
    "alpha" numeric(18,6),
    "sortino" numeric(18,6),
    "sortinoii" numeric(18,6),
    "ret_improper" numeric(18,6),
    "ret_selectivity" numeric(18,6),
    "down_probability" numeric(18,6),
    "rsquared" numeric(18,6),
    "trackingerror" numeric(18,6),
    "down_risk" numeric(18,6),
    "sd_annualised" numeric(18,6),
    "informationratio" numeric(18,6),
    PRIMARY KEY ("schemecode")
);

-- Column Comment
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."schemecode" IS 'Accord Fintech’s Scheme Code';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."upddate" IS 'Updation Date';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."datefrom" IS 'Ratio calculation ‘from’ date range';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."dateto" IS 'Ratio calculation ‘to’ date range';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."average" IS 'Average Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."bmaverage" IS 'Average Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sd" IS 'Standard Deviation of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."bmsd" IS 'Standard Deviation of the benchmark';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."semisd" IS 'Semi Standard Deviation of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."semisdii" IS 'Semi Standard Deviation of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."beta" IS 'Beta of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."correlation" IS 'Corelation of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."beta_corelation" IS 'Beta Corelation of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."covariance" IS 'Covariance of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."treynor" IS 'Treynor Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."fama" IS 'FAMA Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sharpe" IS 'Sharpe Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sharpe_x" IS 'Sharpe Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sharpe_y" IS 'Sharpe Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."jalpha_x" IS 'Jenson’s Alpha of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."jalpha_y" IS 'Jenson’s Alpha of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sortino_x" IS 'SORTINO Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sortino_y" IS 'SORTINO Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."flag" IS 'Updation Flag';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."alpha" IS 'Alpha of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sortino" IS 'SORTINO Ratio of the scheme';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sortinoii" IS 'SORTINO Ratio of the benchmark index';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."ret_improper" IS 'Return due to Improper';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."ret_selectivity" IS 'Return due to Selectivity';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."down_probability" IS 'Downside Probability';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."rsquared" IS 'R-Squared';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."trackingerror" IS 'Tracking Error';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."down_risk" IS 'Down Side Risk - Ignore this field';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."sd_annualised" IS 'SD Annualised -  Ignore this field';
COMMENT ON COLUMN "public"."mf_ratios_defaultbm"."informationratio" IS 'Information Ratio';

-- Table Definition
CREATE TABLE "public"."mfbulkdeals" (
    "fincode" int8 NOT NULL,
    "date" timestamp NOT NULL,
    "exchange" varchar(50),
    "clientname" varchar(255) NOT NULL,
    "type" varchar(50),
    "mfcode" int8,
    "dealtype" varchar(5) NOT NULL,
    "volume" numeric(18,6) NOT NULL,
    "price" numeric(18,6) NOT NULL,
    "flag" varchar(1),
    PRIMARY KEY ("fincode","date","clientname","dealtype","volume","price")
);

-- Table Definition
CREATE TABLE "public"."scheme_assetalloc" (
    "schemeinv_id" int8,
    "schemecode" int8 NOT NULL,
    "investment" varchar(500) NOT NULL,
    "mininv" numeric(18,6),
    "maxinv" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","investment")
);

-- Table Definition
CREATE TABLE "public"."scheme_name_change" (
    "amc_code" int8 NOT NULL,
    "schemecode" int8 NOT NULL,
    "effectivedate" timestamp NOT NULL,
    "oldname" varchar(255),
    "newname" varchar(255),
    "flag" varchar(1),
    PRIMARY KEY ("amc_code","schemecode","effectivedate")
);

-- Table Definition
CREATE TABLE "public"."scheme_eq_details" (
    "schemecode" int8 NOT NULL,
    "monthend" int8 NOT NULL,
    "mcap" numeric(18,6),
    "pe" numeric(18,6),
    "pb" numeric(18,6),
    "div_yield" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","monthend")
);

-- Column Comment
COMMENT ON COLUMN "public"."scheme_eq_details"."schemecode" IS 'Accord Fintech’s Schemecode';
COMMENT ON COLUMN "public"."scheme_eq_details"."monthend" IS 'Month End';
COMMENT ON COLUMN "public"."scheme_eq_details"."mcap" IS 'Market Cap';
COMMENT ON COLUMN "public"."scheme_eq_details"."pe" IS 'PE Ratio';
COMMENT ON COLUMN "public"."scheme_eq_details"."pb" IS 'PB Ratio';
COMMENT ON COLUMN "public"."scheme_eq_details"."div_yield" IS 'Div Yield';
COMMENT ON COLUMN "public"."scheme_eq_details"."flag" IS 'Updation Flag';

-- Table Definition
CREATE TABLE "public"."mf_cagr_return" (
    "schemecode" int8 NOT NULL,
    "c_date" timestamp NOT NULL,
    "p_date" timestamp,
    "c_nav" numeric(18,6),
    "p_nav" numeric(18,6),
    "1dayret" numeric(18,6),
    "1weekdate" timestamp,
    "1weeknav" numeric(18,6),
    "1weekret" numeric(18,6),
    "1mthdate" timestamp,
    "1mthnav" numeric(18,6),
    "1monthret" numeric(18,6),
    "3mthdate" timestamp,
    "3mthnav" numeric(18,6),
    "3monthret" numeric(18,6),
    "6mntdate" timestamp,
    "6mnthnav" numeric(18,6),
    "6monthret" numeric(18,6),
    "9mnthdate" timestamp,
    "9mnthnav" numeric(18,6),
    "9mnthret" numeric(18,6),
    "1yrdate" timestamp,
    "1yrnav" numeric(18,6),
    "1yrret" numeric(18,6),
    "2yrdate" timestamp,
    "2yrnav" numeric(18,6),
    "2yearret" numeric(18,6),
    "3yrdate" timestamp,
    "3yrnav" numeric(18,6),
    "3yearret" numeric(18,6),
    "4yrdate" timestamp,
    "4yrnav" numeric(18,6),
    "4yearret" numeric(18,6),
    "5yrdate" timestamp,
    "5yrnav" numeric(18,6),
    "5yearret" numeric(18,6),
    "incdate" timestamp,
    "incnav" numeric(18,6),
    "incret" numeric(18,6),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","c_date")
);

-- Column Comment
COMMENT ON COLUMN "public"."mf_cagr_return"."schemecode" IS 'Accord Fintech’s Scheme Code';
COMMENT ON COLUMN "public"."mf_cagr_return"."c_date" IS 'Closing Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."p_date" IS 'Previous Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."c_nav" IS 'Closing Nav';
COMMENT ON COLUMN "public"."mf_cagr_return"."p_nav" IS 'Previous Day Nav';
COMMENT ON COLUMN "public"."mf_cagr_return"."1dayret" IS 'One Day Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."1weekdate" IS 'One Week Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."1weeknav" IS 'One Week NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."1weekret" IS 'One Week Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."1mthdate" IS 'One Month Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."1mthnav" IS 'One Month NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."1monthret" IS 'One Month Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."3mthdate" IS 'Three Month Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."3mthnav" IS 'Three Month NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."3monthret" IS 'Three Month Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."6mntdate" IS 'Six Month Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."6mnthnav" IS 'Six Month NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."6monthret" IS 'Six Month Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."9mnthdate" IS 'Nine Month Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."9mnthnav" IS 'Nine Month NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."9mnthret" IS 'Nine Month Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."1yrdate" IS 'One Year Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."1yrnav" IS 'One Year NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."1yrret" IS 'One Year Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."2yrdate" IS 'Two Year Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."2yrnav" IS 'Two Year NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."2yearret" IS 'Two Year Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."3yrdate" IS 'Three Year Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."3yrnav" IS 'Three Year NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."3yearret" IS 'Three Year Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."4yrdate" IS 'Four Year Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."4yrnav" IS 'Four Year NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."4yearret" IS 'Four Year Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."5yrdate" IS 'Five Year Date';
COMMENT ON COLUMN "public"."mf_cagr_return"."5yrnav" IS 'Five Year NAV';
COMMENT ON COLUMN "public"."mf_cagr_return"."5yearret" IS 'Five Year Return';
COMMENT ON COLUMN "public"."mf_cagr_return"."incdate" IS 'Inception date';
COMMENT ON COLUMN "public"."mf_cagr_return"."incnav" IS 'NAV on inception date';
COMMENT ON COLUMN "public"."mf_cagr_return"."incret" IS 'Return on inception date';
COMMENT ON COLUMN "public"."mf_cagr_return"."flag" IS 'Updation flag';

-- Table Definition
CREATE TABLE "public"."ratio_3year_monthlyret" (
    "schemecode" int8 NOT NULL,
    "ratiodate" timestamp NOT NULL,
    "average_nav" numeric(18,6),
    "sd_nav" numeric(18,6),
    "semisd" numeric(18,6),
    "beta" numeric(18,6),
    "corel" numeric(18,6),
    "betacorel" numeric(18,6),
    "rsq" numeric(18,6),
    "trey" numeric(18,6),
    "fama" numeric(18,6),
    "sharpe" numeric(18,6),
    "jalpha" numeric(18,6),
    "sortino" numeric(18,6),
    "retdueimp" numeric(18,6),
    "retduesel" numeric(18,6),
    "downsideprob" numeric(18,6),
    "downsiderisk" numeric(18,6),
    "sortinosd" numeric(18,6),
    "trackingerror" numeric(18,6),
    "informationratio" numeric(18,6),
    "sdann" numeric(18,6),
    "avgindex" numeric(18,6),
    "sd_index" numeric(18,6),
    "covar" numeric(18,6),
    "maxret" numeric(18,6),
    "minret" numeric(18,6),
    "rfr" numeric(18,6),
    "priceindex" int8,
    "priceindexname" varchar(255),
    "flag" varchar(1),
    PRIMARY KEY ("schemecode","ratiodate")
);



-- Indices
CREATE INDEX amc_mst_new_mf_type_idx ON public.amc_mst_new USING btree (mf_type);
CREATE INDEX amc_mst_new_amc_symbol_idx ON public.amc_mst_new USING btree (amc_symbol);


-- Indices
CREATE INDEX amc_keypersons_amc_code_idx ON public.amc_keypersons USING btree (amc_code);


-- Indices
CREATE UNIQUE INDEX scheme_master_schemecode_idx ON public.scheme_master USING btree (schemecode);
CREATE INDEX scheme_master_amc_code_idx ON public.scheme_master USING btree (amc_code);
CREATE INDEX scheme_master_scheme_name_idx ON public.scheme_master USING btree (scheme_name);


-- Indices
CREATE INDEX scheme_details_schemecode_idx ON public.scheme_details USING btree (schemecode);
CREATE INDEX scheme_details_amc_code_idx ON public.scheme_details USING btree (amc_code);
CREATE INDEX scheme_details_amfi_code_idx ON public.scheme_details USING btree (amfi_code);
CREATE INDEX scheme_details_cams_code_idx ON public.scheme_details USING btree (cams_code);
CREATE INDEX scheme_details_isin_code_idx ON public.scheme_details USING btree (isin_code);


-- Indices
CREATE INDEX scheme_rtcode_schemecode_idx ON public.scheme_rtcode USING btree (schemecode);
CREATE INDEX scheme_rtcode_rtschemecode_idx ON public.scheme_rtcode USING btree (rtschemecode);


-- Indices
CREATE INDEX schemeisinmaster_isin_idx ON public.schemeisinmaster USING btree (isin);
CREATE INDEX schemeisinmaster_schemecode_idx ON public.schemeisinmaster USING btree (schemecode);
CREATE INDEX schemeisinmaster_amc_code_idx ON public.schemeisinmaster USING btree (amc_code);
CREATE INDEX schemeisinmaster_nsesymbol_idx ON public.schemeisinmaster USING btree (nsesymbol);
CREATE INDEX schemeisinmaster_rtaschemecode_idx ON public.schemeisinmaster USING btree (rtaschemecode);
CREATE INDEX schemeisinmaster_series_idx ON public.schemeisinmaster USING btree (series);


-- Indices
CREATE INDEX type_mst_type_code_idx ON public.type_mst USING btree (type_code);
CREATE INDEX type_mst_type_idx ON public.type_mst USING btree (type);


-- Indices
CREATE INDEX option_mst_opt_code_idx ON public.option_mst USING btree (opt_code);


-- Indices
CREATE INDEX sclass_mst_category_idx ON public.sclass_mst USING btree (category);
CREATE INDEX sclass_mst_sub_category_idx ON public.sclass_mst USING btree (sub_category);
CREATE INDEX sclass_mst_classcode_idx ON public.sclass_mst USING btree (classcode);
CREATE INDEX sclass_mst_classname_idx ON public.sclass_mst USING btree (classname);
CREATE INDEX sclass_mst_asset_code_idx ON public.sclass_mst USING btree (asset_code);
CREATE INDEX sclass_mst_asset_type_idx ON public.sclass_mst USING btree (asset_type);


-- Indices
CREATE INDEX rt_mst_rt_code_idx ON public.rt_mst USING btree (rt_code);
CREATE INDEX rt_mst_rt_name_idx ON public.rt_mst USING btree (rt_name);


-- Indices
CREATE INDEX plan_mst_plan_code_idx ON public.plan_mst USING btree (plan_code);
CREATE INDEX plan_mst_plan_idx ON public.plan_mst USING btree (plan);


-- Indices
CREATE INDEX cust_mst_cust_code_idx ON public.cust_mst USING btree (cust_code);
CREATE INDEX cust_mst_cust_name_idx ON public.cust_mst USING btree (cust_name);
CREATE INDEX cust_mst_sebi_reg_no_idx ON public.cust_mst USING btree (sebi_reg_no);


-- Indices
CREATE INDEX fundmanager_mst_fundmanager_idx ON public.fundmanager_mst USING btree (fundmanager);
CREATE INDEX fundmanager_mst_id_idx ON public.fundmanager_mst USING btree (id);


-- Indices
CREATE INDEX div_mst_div_code_idx ON public.div_mst USING btree (div_code);
CREATE INDEX div_mst_div_type_idx ON public.div_mst USING btree (div_type);


-- Indices
CREATE INDEX scheme_objective_schemecode_idx ON public.scheme_objective USING btree (schemecode);


-- Indices
CREATE INDEX mf_sip_schemecode_idx ON public.mf_sip USING btree (schemecode);
CREATE INDEX mf_sip_amc_code_idx ON public.mf_sip USING btree (amc_code);


-- Indices
CREATE INDEX mf_swp_schemecode_idx ON public.mf_swp USING btree (schemecode);
CREATE INDEX mf_swp_amc_code_idx ON public.mf_swp USING btree (amc_code);


-- Indices
CREATE INDEX mf_stp_schemecode_idx ON public.mf_stp USING btree (schemecode);
CREATE INDEX mf_stp_amc_code_idx ON public.mf_stp USING btree (amc_code);


-- Indices
CREATE INDEX scheme_index_part_schemecode_idx ON public.scheme_index_part USING btree (schemecode);
CREATE INDEX scheme_index_part_indexcode_idx ON public.scheme_index_part USING btree (indexcode);


-- Indices
CREATE UNIQUE INDEX index_mst_indexcode_idx ON public.index_mst USING btree (indexcode);
CREATE INDEX index_mst_fincode_idx ON public.index_mst USING btree (fincode);
CREATE INDEX index_mst_scripcode_idx ON public.index_mst USING btree (scripcode);


-- Indices
CREATE INDEX schemeload_lsrno_idx ON public.schemeload USING btree (lsrno);
CREATE INDEX schemeload_schemecode_idx ON public.schemeload USING btree (schemecode);
CREATE INDEX schemeload_ltypecode_idx ON public.schemeload USING btree (ltypecode);


-- Indices
CREATE INDEX loadtype_mst_ltypecode_idx ON public.loadtype_mst USING btree (ltypecode);


-- Indices
CREATE INDEX companymaster_s_name_idx ON public.companymaster USING btree (s_name);
CREATE INDEX companymaster_fincode_idx ON public.companymaster USING btree (fincode);
CREATE INDEX companymaster_scripcode_idx ON public.companymaster USING btree (scripcode);
CREATE INDEX companymaster_symbol_idx ON public.companymaster USING btree (symbol);
CREATE INDEX companymaster_ind_code_idx ON public.companymaster USING btree (ind_code);
CREATE INDEX companymaster_compname_idx ON public.companymaster USING btree (compname);
CREATE INDEX companymaster_industry_idx ON public.companymaster USING btree (industry);
CREATE INDEX companymaster_status_idx ON public.companymaster USING btree (status);


-- Indices
CREATE INDEX industry_mst_ind_code_idx ON public.industry_mst USING btree (ind_code);


-- Indices
CREATE INDEX asect_mst_asect_code_idx ON public.asect_mst USING btree (asect_code);
CREATE INDEX asect_mst_asect_type_idx ON public.asect_mst USING btree (asect_type);
CREATE INDEX asect_mst_asset_idx ON public.asect_mst USING btree (asset);


-- Indices
CREATE INDEX scheme_rgess_schemecode_idx ON public.scheme_rgess USING btree (schemecode);


-- Indices
CREATE INDEX mf_portfolio_schemecode_idx ON public.mf_portfolio USING btree (schemecode);
CREATE INDEX mf_portfolio_fincode_idx ON public.mf_portfolio USING btree (fincode);
CREATE INDEX mf_portfolio_asect_code_idx ON public.mf_portfolio USING btree (asect_code);
CREATE INDEX mf_portfolio_sect_code_idx ON public.mf_portfolio USING btree (sect_code);
CREATE INDEX mf_portfolio_compname_idx ON public.mf_portfolio USING btree (compname);


-- Indices
CREATE INDEX amc_paum_amc_code_idx ON public.amc_paum USING btree (amc_code);
CREATE UNIQUE INDEX pk_amc_paum ON public.amc_paum USING btree (amc_code, aumdate);


-- Indices
CREATE INDEX scheme_paum_schemecode_idx ON public.scheme_paum USING btree (schemecode);
CREATE INDEX scheme_paum_amc_code_idx ON public.scheme_paum USING btree (amc_code);


-- Indices
CREATE INDEX amc_aum_amc_code_idx ON public.amc_aum USING btree (amc_code);


-- Indices
CREATE INDEX scheme_aum_schemecode_idx ON public.scheme_aum USING btree (schemecode);


-- Indices
CREATE INDEX portfolio_inout_fincode_idx ON public.portfolio_inout USING btree (fincode);


-- Indices
CREATE INDEX avg_scheme_aum_schemecode_idx ON public.avg_scheme_aum USING btree (schemecode);


-- Indices
CREATE INDEX currentnav_schemecode_idx ON public.currentnav USING btree (schemecode);


-- Indices
CREATE INDEX navhist_hl_schemecode_idx ON public.navhist_hl USING btree (schemecode);


-- Indices
CREATE INDEX idx_amc_code_avg_maturity ON public.avg_maturity USING btree (amc_code);
CREATE INDEX idx_schemecode_avg_maturity ON public.avg_maturity USING btree (schemecode);
CREATE INDEX idx_amc_code_schemecode_avg_maturity ON public.avg_maturity USING btree (amc_code, schemecode);


-- Indices
CREATE INDEX mf_return_schemecode_idx ON public.mf_return USING btree (schemecode);
CREATE UNIQUE INDEX pk_mf_return ON public.mf_return USING btree (schemecode);


-- Indices
CREATE INDEX mf_cagr_return_schemecode_idx ON public.mf_cagr_return USING btree (schemecode);
CREATE INDEX mf_cagr_return_c_date_idx ON public.mf_cagr_return USING btree (c_date);


-- Indices
CREATE INDEX ix_ratio_3year_monthlyret_schemecode ON public.ratio_3year_monthlyret USING btree (schemecode);
CREATE INDEX ix_ratio_3year_monthlyret_ratiodate ON public.ratio_3year_monthlyret USING btree (ratiodate);
