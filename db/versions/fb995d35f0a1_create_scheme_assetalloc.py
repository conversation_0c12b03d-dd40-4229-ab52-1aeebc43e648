"""create_scheme_assetalloc

Revision ID: fb995d35f0a1
Revises: 0bc996a7fece
Create Date: 2025-04-27 11:40:05.793520

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "fb995d35f0a1"
down_revision: Union[str, None] = "0bc996a7fece"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""

    op.create_table(
        "scheme_assetalloc",
        sa.Column(
            "schemeinv_id", sa.BigInteger(), nullable=True
        ),  # Integer -> BigInteger [4]
        sa.Column(
            "schemecode", sa.BigInteger(), nullable=False
        ),  # Integer -> BigInteger [4]
        sa.Column(
            "investment", sa.String(length=500), nullable=False
        ),  # Varchar(500) [4]
        sa.Column(
            "mininv", sa.Numeric(precision=18, scale=6), nullable=True
        ),  # Float -> Numeric(18,6) [4]
        sa.Column(
            "maxinv", sa.Numeric(precision=18, scale=6), nullable=True
        ),  # Float -> Numeric(18,6) [5]
        sa.Column("flag", sa.String(length=1), nullable=True),  # Varchar(1) [5]
        # Composite Primary Key based on Source [4]
        sa.PrimaryKeyConstraint("schemecode", "investment"),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_assetalloc")
