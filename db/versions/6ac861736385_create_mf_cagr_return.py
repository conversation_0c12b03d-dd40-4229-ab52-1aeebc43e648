"""create_mf_cagr_return

Revision ID: 6ac861736385
Revises: 47dfc17c2f1b
Create Date: 2025-06-14 19:34:46.201385

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "6ac861736385"
down_revision: Union[str, None] = "47dfc17c2f1b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to include mf_cagr_return table."""
    op.create_table(
        "mf_cagr_return",
        sa.Column(
            "schemecode",
            sa.BigInteger(),  # Mapped to Integer for consistency as discussed [4-54]
            nullable=False,
            comment="Accord Fintech’s Scheme Code",  # [1]
        ),
        sa.Column(
            "c_date", sa.DateTime(), nullable=False, comment="Closing Date"  # [1]
        ),
        sa.Column(
            "p_date", sa.DateTime(), nullable=True, comment="Previous Date"
        ),  # [1]
        sa.Column(
            "c_nav", sa.Numeric(18, 6), nullable=True, comment="Closing Nav"
        ),  # [1]
        sa.Column(
            "p_nav", sa.Numeric(18, 6), nullable=True, comment="Previous Day Nav"
        ),  # [1]
        sa.Column(
            "1dayret", sa.Numeric(18, 6), nullable=True, comment="One Day Return"
        ),  # [1]
        sa.Column(
            "1weekdate", sa.DateTime(), nullable=True, comment="One Week Date"
        ),  # [1]
        sa.Column(
            "1weeknav", sa.Numeric(18, 6), nullable=True, comment="One Week NAV"
        ),  # [1]
        sa.Column(
            "1weekret", sa.Numeric(18, 6), nullable=True, comment="One Week Return"
        ),  # [1]
        sa.Column(
            "1mthdate", sa.DateTime(), nullable=True, comment="One Month Date"
        ),  # [1]
        sa.Column(
            "1mthnav", sa.Numeric(18, 6), nullable=True, comment="One Month NAV"
        ),  # [1]
        sa.Column(
            "1monthret", sa.Numeric(18, 6), nullable=True, comment="One Month Return"
        ),  # [1]
        sa.Column(
            "3mthdate", sa.DateTime(), nullable=True, comment="Three Month Date"
        ),  # [1]
        sa.Column(
            "3mthnav", sa.Numeric(18, 6), nullable=True, comment="Three Month NAV"
        ),  # [1]
        sa.Column(
            "3monthret", sa.Numeric(18, 6), nullable=True, comment="Three Month Return"
        ),  # [55]
        sa.Column(
            "6mntdate", sa.DateTime(), nullable=True, comment="Six Month Date"
        ),  # [55]
        sa.Column(
            "6mnthnav", sa.Numeric(18, 6), nullable=True, comment="Six Month NAV"
        ),  # [55]
        sa.Column(
            "6monthret", sa.Numeric(18, 6), nullable=True, comment="Six Month Return"
        ),  # [55]
        sa.Column(
            "9mnthdate", sa.DateTime(), nullable=True, comment="Nine Month Date"
        ),  # [55]
        sa.Column(
            "9mnthnav", sa.Numeric(18, 6), nullable=True, comment="Nine Month NAV"
        ),  # [55]
        sa.Column(
            "9mnthret", sa.Numeric(18, 6), nullable=True, comment="Nine Month Return"
        ),  # [55]
        sa.Column(
            "1yrdate", sa.DateTime(), nullable=True, comment="One Year Date"
        ),  # [55]
        sa.Column(
            "1yrnav", sa.Numeric(18, 6), nullable=True, comment="One Year NAV"
        ),  # [55]
        sa.Column(
            "1yrret", sa.Numeric(18, 6), nullable=True, comment="One Year Return"
        ),  # [55]
        sa.Column(
            "2yrdate", sa.DateTime(), nullable=True, comment="Two Year Date"
        ),  # [56]
        sa.Column(
            "2yrnav", sa.Numeric(18, 6), nullable=True, comment="Two Year NAV"
        ),  # [56]
        sa.Column(
            "2yearret", sa.Numeric(18, 6), nullable=True, comment="Two Year Return"
        ),  # [56]
        sa.Column(
            "3yrdate", sa.DateTime(), nullable=True, comment="Three Year Date"
        ),  # [56]
        sa.Column(
            "3yrnav", sa.Numeric(18, 6), nullable=True, comment="Three Year NAV"
        ),  # [56]
        sa.Column(
            "3yearret", sa.Numeric(18, 6), nullable=True, comment="Three Year Return"
        ),  # [56]
        sa.Column(
            "4yrdate", sa.DateTime(), nullable=True, comment="Four Year Date"
        ),  # [56]
        sa.Column(
            "4yrnav", sa.Numeric(18, 6), nullable=True, comment="Four Year NAV"
        ),  # [56]
        sa.Column(
            "4yearret", sa.Numeric(18, 6), nullable=True, comment="Four Year Return"
        ),  # [56]
        sa.Column(
            "5yrdate", sa.DateTime(), nullable=True, comment="Five Year Date"
        ),  # [56]
        sa.Column(
            "5yrnav", sa.Numeric(18, 6), nullable=True, comment="Five Year NAV"
        ),  # [56]
        sa.Column(
            "5yearret", sa.Numeric(18, 6), nullable=True, comment="Five Year Return"
        ),  # [56]
        sa.Column(
            "incdate", sa.DateTime(), nullable=True, comment="Inception date"
        ),  # [56]
        sa.Column(
            "incnav", sa.Numeric(18, 6), nullable=True, comment="NAV on inception date"
        ),  # [56]
        sa.Column(
            "incret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return on inception date",
        ),  # [56]
        sa.Column(
            "flag", sa.String(length=1), nullable=True, comment="Updation flag"
        ),  # [56]
        sa.PrimaryKeyConstraint(
            "schemecode", "c_date"
        ),  # Composite PK based on schema and data characteristics
    )
    op.execute(
        "COMMIT;"
    )  # Commit transaction for concurrent index creation (PostgreSQL specific)

    # Creating indexes for efficient querying
    op.create_index(
        index_name="mf_cagr_return_schemecode_idx",
        table_name="mf_cagr_return",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_cagr_return_c_date_idx",
        table_name="mf_cagr_return",
        columns=["c_date"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("mf_cagr_return")
