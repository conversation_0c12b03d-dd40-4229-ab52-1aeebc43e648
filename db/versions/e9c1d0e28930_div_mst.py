"""div_mst

Revision ID: e9c1d0e28930
Revises: a461bd8a6b4f
Create Date: 2025-03-09 16:59:55.896057

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e9c1d0e28930"
down_revision: Union[str, None] = "a461bd8a6b4f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "div_mst",
        sa.Column("div_code", sa.Float(), primary_key=True, nullable=False),
        sa.Column("div_type", sa.String(length=30), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="div_mst_div_code_idx",
        table_name="div_mst",
        columns=["div_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="div_mst_div_type_idx",
        table_name="div_mst",
        columns=["div_type"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("div_mst")
