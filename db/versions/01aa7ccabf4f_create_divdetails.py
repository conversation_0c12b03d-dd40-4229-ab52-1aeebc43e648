"""create_divdetails

Revision ID: 01aa7ccabf4f
Revises: 59f1c442ca5a
Create Date: 2025-04-27 11:03:19.617490

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "01aa7ccabf4f"
down_revision: Union[str, None] = "59f1c442ca5a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    Creates the Divdetails table based on the provided schema.
    This table contains Scheme dividend details.
    """
    op.create_table(
        "divdetails",
        sa.Column(
            "amc_code",
            sa.BigInteger,
            nullable=True,
            comment="AMC Code is unique for each company",
        ),  # [2]
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Scheme Code",
        ),  # [2]
        sa.Column(
            "recorddate", sa.DateTime, primary_key=True, comment="Record date"
        ),  # [2]
        sa.Column(
            "div_code",
            sa.BigInteger,
            nullable=True,
            comment="Dividend code which comes from div_mst",
        ),  # [2]
        sa.Column(
            "exdivdate", sa.DateTime, nullable=True, comment="Ex-dividend date"
        ),  # [2]
        sa.Column(
            "bonusrate1", sa.Numeric(18, 6), nullable=True, comment="Bonus rate1"
        ),  # [2]
        sa.Column(
            "bonusrate2", sa.Numeric(18, 6), nullable=True, comment="Bonus rate2"
        ),  # [2]
        sa.Column(
            "gross", sa.Numeric(18, 6), nullable=True, comment="Dividend percentage"
        ),  # [2]
        sa.Column(
            "corporate",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Corporate dividend percentage",
        ),  # [2]
        sa.Column(
            "noncorporate",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Non Corporate Dividend Percentage",
        ),  # [2]
        sa.Column(
            "status",
            sa.VARCHAR(3),
            nullable=True,
            comment="Status. Indicates unit of dividend % or P/U value",
        ),  # [2]
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),  # [2]
        # Primary key is composite: schemecode and recorddate based on the source [2]
        sa.PrimaryKeyConstraint("schemecode", "recorddate"),
    )


def downgrade():
    """
    Drops the Divdetails table.
    """
    op.drop_table("divdetails")
