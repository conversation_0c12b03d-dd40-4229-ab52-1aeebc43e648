from typing import Optional


class ApiException(Exception):
    def __init__(self, msg: str, code: str, http_error_code: Optional[int] = None):
        self.msg: str = msg
        self.code: str = code
        self.http_error_code: Optional[int] = http_error_code
        super().__init__(self.msg)


class NotFoundException(ApiException):
    def __init__(self, msg: str, code: str = "not_found", http_error_code=404):
        super().__init__(msg=msg, code=code, http_error_code=http_error_code)


class UnauthorizedException(ApiException):
    def __init__(self, msg: str, code: str = "unauthorized", http_error_code=401):
        super().__init__(msg=msg, code=code, http_error_code=http_error_code)


class BadRequestException(ApiException):
    def __init__(self, msg: str, code: str = "bad_request", http_error_code=400):
        super().__init__(msg=msg, code=code, http_error_code=http_error_code)


class ForbiddenException(ApiException):
    def __init__(self, msg: str, code: str = "forbidden", http_error_code=403):
        super().__init__(msg=msg, code=code, http_error_code=http_error_code)


class PreconditionFailedException(ApiException):
    def __init__(
        self, msg: str, code: str = "pre_condition_failed", http_error_code=412
    ):
        super().__init__(msg=msg, code=code, http_error_code=http_error_code)


class ValueErrorException(ApiException):
    def __init__(self, msg: str, code: str = "value_error", http_error_code=400):
        super().__init__(msg=msg, code=code, http_error_code=http_error_code)
