from fastapi import Request, APIRouter, status, Depends, Response, Header
from fastapi.responses import JSONResponse, RedirectResponse
from utils.datetime_support import datetime, asia_kolkata
from src.routers.base_controller import (
    success_response,
    failure_response,
    API_VERSION,
    log_request_body,
)
from fastapi import HTTPException


router = APIRouter(include_in_schema=False, prefix="/k")


@router.get("/api/v1/healthcheck", name="healthcheck", include_in_schema=False)
async def healthcheck(request: Request):
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=success_response(
            payload={
                "service": "kairo_api",
                "time": datetime.now(tz=asia_kolkata).isoformat(),
                "version": API_VERSION,
            }
        ),
    )


@router.get("/up", name="up", include_in_schema=False)
async def healthcheck(request: Request):
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=success_response(
            payload={
                "status": "up",
                "service": "axis_tube_service",
                "time": datetime.now(tz=asia_kolkata).isoformat(),
                "version": API_VERSION,
            }
        ),
    )


@router.get("/api/_internal_error", include_in_schema=False)
async def internal_error(request: Request):
    raise HTTPException(status_code=500, detail="Internal Error")
