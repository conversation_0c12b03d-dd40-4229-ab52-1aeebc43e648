ARG PYTHON_VERSION=3.12

ARG DEBIAN_VERSION=bookworm

FROM python:${PYTHON_VERSION}-${DEBIAN_VERSION}

ENV KAIRO_APP_DIR=/app

RUN mkdir $KAIRO_APP_DIR

WORKDIR $KAIRO_APP_DIR

RUN mkdir -p ${KAIRO_APP_DIR}/data/uploads

RUN apt-get update -y && apt-get install -y \
    curl \
    build-essential \
    libssl-dev \
    libffi-dev \ 
git \ 
postgresql-client \
    telnet \
    net-tools \ 
libavcodec-extra \
    ffmpeg \
    libavcodec-extra

COPY poetry.lock $KAIRO_APP_DIR
COPY pyproject.toml $KAIRO_APP_DIR

ENV PYTHONPATH=$KAIRO_APP_DIR
ENV POETRY_VIRTUALENVS_PATH=$KAIRO_APP_DIR/

RUN pip install poetry
RUN poetry install --no-root

ADD . $KAIRO_APP_DIR/
EXPOSE 8080 9005 5567

CMD ["/bin/bash", "-c", "./launch.sh"]
