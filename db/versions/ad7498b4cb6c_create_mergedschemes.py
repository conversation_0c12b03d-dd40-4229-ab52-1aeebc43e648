"""create_mergedschemes

Revision ID: ad7498b4cb6c
Revises: 2fd2b4ef2779
Create Date: 2025-04-27 11:24:37.230454

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "ad7498b4cb6c"
down_revision: Union[str, None] = "2fd2b4ef2779"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the mergedschemes table based on the provided schema.
    This table contains data about schemes that have been merged.
    """
    op.create_table(
        "mergedschemes",  # Table name in lowercase as requested
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Schemecode",
        ),  # Using BigInteger as requested
        sa.Column(
            "mergedwith",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Schemecode",
        ),  # Using <PERSON>Integer as requested
        sa.Column(
            "effect_date", sa.DateTime, nullable=True, comment="Effective Date"
        ),  # Datetime from source [2]
        sa.Column(
            "flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"
        ),  # Varchar(1) from source [2]
        # Primary key is composite: schemecode and mergedwith based on the source [2]
        sa.PrimaryKeyConstraint("schemecode", "mergedwith"),  #
    )


def downgrade():
    """
    Drops the mergedschemes table.
    """
    op.drop_table("mergedschemes")
