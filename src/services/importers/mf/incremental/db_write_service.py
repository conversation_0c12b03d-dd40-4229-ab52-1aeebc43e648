import os
import csv
import asyncpg
from typing import List, Optional, Dict
from config.cfg import cfg
from os import PathLike
from datetime import datetime, date
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncConnection
from connectors.async_pg import AsyncSession, get_async_session
from utils.datetime_support import asia_kolkata
from config.logger import log_info, log_error
from utils.random import generate


class CSVFileInfo(BaseModel):
    file_path: PathLike
    headers: List[str] = None
    data: List[List[str]] = None
    table_name: Optional[str] = None
    temp_table_name: Optional[str] = None


class TableService:
    @staticmethod
    async def get_table_name(file_path: PathLike) -> str:
        """Extracts the table name from the file path."""
        table_name: str = str(file_path).lower().split("/")[-1].replace(".csv", "")
        return table_name

    @staticmethod
    async def get_temporary_table_name(
        table_name: str, process_date: Optional[str] = None
    ) -> str:
        """Generates a temporary table name based on the original table name."""
        if not process_date:
            process_date: str = generate(size=8)
        return f"temp_{table_name}_{process_date}"


class DBWriteService:
    @staticmethod
    async def run(db_session: AsyncSession, current_date: Optional[str] = None):
        """Launches the DBWriteService to process the folder and write data to the database.
        Uses the current date to determine the folder path for CSV files.
        If no current date is provided, it defaults to the current date in Asia/Kolkata timezone.
        This method initializes the DBWriteService with the database session and the folder path,
        and then invokes the service to process the folder.

        Args:
            db_session (AsyncSession): The database session to be used for writing data.
            current_date (Optional[str], optional): The current date in "dd-mm-yyyy" format.
        """
        if not current_date:
            current_date: date = datetime.now().astimezone(asia_kolkata).date()
        else:
            current_date: date = datetime.strptime(current_date, "%d-%m-%Y").date()
        current_date_str: str = current_date.strftime("%d%m%Y")
        db_session: AsyncSession = await get_async_session()
        folder_path: PathLike = cfg("KAIRO_MF_INCREMENTAL_DATA_FOLDER")
        csv_folder_path = os.path.join(folder_path, current_date_str, "csv")
        log_info(msg=f"Processing folder: {csv_folder_path}")
        db_write_service = DBWriteService(
            db_session=db_session, folder_path=csv_folder_path
        )
        await db_write_service.invoke()

    def __init__(self, db_session: AsyncSession, folder_path: PathLike):
        self.db_session: AsyncSession = db_session
        self.folder_path: PathLike = folder_path
        self.process_date: str = str(folder_path).split("/")[-2]

    async def invoke(self) -> None:
        """Invoke the DBWriteService to process the folder containing CSV files and write data to the database.

        Returns:
            _type_: None
        """
        return await self.process_folder(
            folder_path=self.folder_path,
            db_session=self.db_session,
            process_date=self.process_date,
        )

    @staticmethod
    async def process_folder(
        folder_path: PathLike, db_session: AsyncSession, process_date: str
    ) -> None:
        """Loops through all files in the specified folder and processes each file.
        This method reads each file in the folder, processes it, and writes the data to the database.

        Args:
            folder_path (PathLike): Path to the folder containing CSV files.
            db_session (AsyncSession): The database session to be used for writing data.
            process_date (str): The date when the files are being processed, typically in "ddmmyyyy" format.
        """
        for filename in sorted(os.listdir(folder_path)):
            file_path: PathLike = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                # Process the file and write to the database
                await DBWriteService.process_file(
                    file_path=file_path,
                    db_session=db_session,
                    process_date=process_date,
                )

    @staticmethod
    async def process_file(
        file_path: PathLike, db_session: AsyncSession, process_date: str
    ) -> None:
        """Processes a single CSV file and writes its data to the database.
        This method reads the CSV file, creates a temporary table in the database,
        and copies the data from the CSV file into the temporary table.

        Finally, it merges the data into the main table in the database.

        Args:
            file_path (PathLike): Path to the CSV file to be processed.
            db_session (AsyncSession): The database session to be used for writing data.
            process_date (str): The date when the file is being processed, typically in "ddmmyyyy" format.
        """
        log_info(msg=f"Processing file: {file_path}")

        csv_file_info: CSVFileInfo = await DBWriteService.read_csv(
            file_path=file_path, process_date=process_date
        )
        await DBWriteService.create_temp_table_and_copy_csv(
            db_session=db_session,
            csv_file_info=csv_file_info,
        )

    @staticmethod
    async def create_temp_table_and_copy_csv(
        db_session: AsyncSession,
        csv_file_info: CSVFileInfo,
    ) -> None:
        """Creates a temporary table in the database based on the CSV file headers,
        copies the data from the CSV file into the temporary table,

        Args:
            db_session (AsyncSession): The database session to be used for writing data.
            csv_file_info (CSVFileInfo): An instance containing information about the CSV file,
                including the file path, headers, data,and table names.

        """
        if csv_file_info.table_name == "ratio_3year_monthlyret":
            print(csv_file_info.headers)
        log_info(
            msg=f"Creating temporary table: {csv_file_info.temp_table_name} with headers: {csv_file_info.headers}"
        )
        temp_table_sql: str = f"""
        CREATE TEMP TABLE IF NOT EXISTS {csv_file_info.temp_table_name} (
            LIKE {csv_file_info.table_name} INCLUDING ALL
        );
        """
        column_list: str = ", ".join(
            [f'"{header}"' for header in csv_file_info.headers]
        )

        pk_columns: List[str] = await DBWriteService.get_primary_key_columns(
            csv_file_info.table_name, db_session=db_session
        )
        pk_columns_str: str = ", ".join([f'"{col}"' for col in pk_columns])
        non_primary_key_columns: List[str] = [
            col for col in csv_file_info.headers if col not in pk_columns
        ]
        set_clause: str = ", ".join(
            [f'"{col}" = EXCLUDED."{col}"' for col in non_primary_key_columns]
        )
        type_mapping: Dict[str, str] = (
            await DBWriteService.get_table_columns_and_datatype(
                db_session=db_session, table_name=csv_file_info.table_name
            )
        )
        column_list_with_casting: str = ", ".join(
            [
                f'"{header}"::{type_mapping.get(header, "TEXT")}'
                for header in csv_file_info.headers
            ]
        )
        write_to_main_table_sql: str = f"""
        INSERT INTO {csv_file_info.table_name} ({column_list})
        SELECT {column_list_with_casting} FROM {csv_file_info.temp_table_name}
        ON CONFLICT ({pk_columns_str}) DO UPDATE SET {set_clause};
        """

        try:
            conn: asyncpg.Connection = await asyncpg.connect(
                cfg("DB_URL_ASYNC_IMPORTER")
            )
            try:
                # Step 1: Create temporary table
                await conn.execute(temp_table_sql)

                # Step 2: Copy CSV data to temporary table
                log_info(msg=f"Copying CSV data to temp_table")
                with open(csv_file_info.file_path, "rb") as f:
                    result = await conn.copy_to_table(
                        csv_file_info.temp_table_name,
                        columns=csv_file_info.headers,
                        source=f,
                        format="csv",
                        header=True,
                        quote='"',
                        delimiter=",",
                    )

                results = await conn.fetch(
                    f"SELECT * from {csv_file_info.temp_table_name} LIMIT 40;"
                )

                # Step 3: Merge data into target table
                log_info(
                    msg=f"Writing data from {csv_file_info.temp_table_name} tp the main table: {csv_file_info.table_name}"
                )
                await conn.execute(write_to_main_table_sql)
                log_info(
                    f"Merged data from {csv_file_info.temp_table_name} to {csv_file_info.table_name}"
                )

                # Step 4: Commit transaction
                await db_session.commit()
            finally:
                await conn.close()
        except Exception as e:
            log_error(
                f"Error processing {csv_file_info.file_path} to {csv_file_info.table_name}: {e}"
            )
            raise e

    @staticmethod
    async def read_csv(file_path: PathLike, process_date: str) -> CSVFileInfo:
        """Read a CSV file into memory

        Args:
            file_path (PathLike): Path to the CSV file to be read.
        """
        table_name: str = await TableService.get_table_name(file_path=file_path)
        temp_table_name: str = await TableService.get_temporary_table_name(
            table_name=table_name, process_date=process_date
        )
        with open(file=file_path, mode="r", encoding="utf-8") as csv_file:
            csv_reader = csv.reader(csv_file)
            headers: List[str] = [h.strip().lower() for h in next(csv_reader)]
            data: List[List[str]] = [row for row in csv_reader]
            return CSVFileInfo(
                file_path=file_path,
                headers=headers,
                data=data,
                table_name=table_name,
                temp_table_name=temp_table_name,
            )

    @staticmethod
    async def get_primary_key_columns(
        table_name: str, db_session: AsyncSession
    ) -> List[str]:
        """Get the primary key columns for a given table in the database.

        Args:
            table_name (str): The name of the table for which to retrieve primary key columns.
            db_session (AsyncSession): The database session to be used for executing the query.
        Returns:
            List[str]: A list of primary key column names for the specified table.
        """

        query: str = f"""
        SELECT
            a.attname AS primary_key_columns
        FROM
            pg_constraint c
            JOIN pg_class t ON t.oid = c.conrelid
            JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(c.conkey)
        WHERE
            c.contype = 'p'
            AND t.relname = :table_name
            AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = :schema_name)
        ORDER BY
            a.attnum;
        """

        # async_conn: AsyncConnection = await db_session.connection()
        result = await db_session.execute(
            text(query), params={"table_name": table_name, "schema_name": "public"}
        )
        primary_key_cols: List[str] = [row[0] for row in result.fetchall()]
        return primary_key_cols

    @staticmethod
    async def get_table_columns_and_datatype(
        db_session: AsyncSession, table_name: str
    ) -> List[str]:
        """Get the columns and their data types for a given table in the database.
        Args:
            db_session (AsyncSession): The database session to be used for executing the query.
            table_name (str): The name of the table for which to retrieve columns and data types.
        Returns:
            List[str]: A list of column names and their corresponding data types for the specified table.
        """

        query: str = f"""
        SELECT
            column_name, data_type
        FROM
            information_schema.columns
        WHERE
            table_name = :table_name
            AND table_schema = 'public';
        """
        type_mapping: Dict[str, str] = {
            "integer": "INTEGER",
            "bigint": "BIGINT",
            "numeric": "NUMERIC(18,6)",
            "date": "DATE",
            "datetime": "TIMESTAMP",
            "timestamp without time zone": "TIMESTAMP",
            "character varying": "VARCHAR",
            "double precision": "DOUBLE PRECISION",
            "text": "TEXT",
            "boolean": "BOOLEAN",
            # Add more mappings as needed
        }
        result = await db_session.execute(
            text(query), params={"table_name": table_name}
        )
        columns_and_types: List[str] = {
            row[0]: type_mapping.get(row[1]) for row in result.fetchall()
        }
        return columns_and_types


if __name__ == "__main__":
    # Example usage
    from connectors.async_pg import get_async_session
    import asyncio
    import os

    async def main():
        db_session: AsyncSession = await get_async_session()
        result = await DBWriteService.get_table_columns_and_datatype(
            db_session=db_session, table_name="amc_paum"
        )
        folder_path: PathLike = cfg("KAIRO_MF_INCREMENTAL_DATA_FOLDER")
        for folder in sorted(os.listdir(folder_path)):
            if folder.startswith("03062025"):
                continue
            if not os.path.isdir(folder_path):
                continue
            csv_folder_path = os.path.join(folder_path, folder, "csv")
            log_info(msg=f"Processing folder: {csv_folder_path}")
            db_write_service = DBWriteService(
                db_session=db_session, folder_path=csv_folder_path
            )

            await db_write_service.invoke()

    # Invoke the service to process the folder

    asyncio.run(main())
