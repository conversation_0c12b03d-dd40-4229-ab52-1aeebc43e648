"""create_option_mst

Revision ID: 175b30ab3c89
Revises: fa2ad27abf72
Create Date: 2025-03-09 16:42:32.816948

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "175b30ab3c89"
down_revision: Union[str, None] = "fa2ad27abf72"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "option_mst",
        sa.Column("opt_code", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("option", sa.String(length=30), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="option_mst_opt_code_idx",
        table_name="option_mst",
        columns=["opt_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("option_mst")
