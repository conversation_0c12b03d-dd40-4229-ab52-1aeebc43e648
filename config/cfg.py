import os


__all__ = ["cfg", "__cfg__"]

env = os.environ.get("KAIRO_ENV", "development")


def is_dev():
    return env == "development"


def is_test():
    return env == "test"


def is_staging():
    return env == "staging"


# constants
DB_URL = os.environ.get("KAIRO_DB_URL", None)
DB_URL_ASYNC = os.environ.get("KAIRO_DB_URL_ASYNC", None)
DB_URL_ASYNC_IMPORTER = os.environ.get("KAIRO_DB_URL_ASYNC_IMPORTER", None)
REDIS_URL = os.environ.get("KAIRO_REDIS_URL", None)
APP_DIR = os.environ.get("KAIRO_APP_DIR", "/app")
REDIS_URL = os.environ.get("KAIRO_REDIS_URL", None)
MF_HISTORICAL_DATA_FOLDER = os.environ.get(
    "KAIRO_MF_HISTORICAL_DATA_FOLDER",
    "/Users/<USER>/Code/PythonApps/Quantsnap/kairo/local_data/mf/20250228",
)
MF_IMPORTER_URL = os.environ.get(
    "KAIRO_MF_IMPORTER_URL",
    "https://contentapi.accordwebservices.com/RawData/GetRawDataJSON",
)
MF_IMPORTER_TOKEN = os.environ.get("KAIRO_MF_IMPORTER_TOKEN", None)
PROXY_USERNAME = os.environ.get("KAIRO_PROXY_USERNAME", None)
PROXY_PASSWORD = os.environ.get("KAIRO_PROXY_PASSWORD", None)
DEFAULT_DATA_FOLDER = os.environ.get(
    "KAIRO_DEFAULT_DATA_FOLDER",
    "/Users/<USER>/Code/PythonApps/Quantsnap/kairo/local_data",
)
MF_INCREMENTAL_DATA_FOLDER = os.environ.get(
    "KAIRO_MF_INCREMENTAL_DATA_FOLDER",
    "/Users/<USER>/Code/PythonApps/Quantsnap/kairo/local_data",
)
PROXY_URL = os.environ.get("KAIRO_PROXY_URL", None)
# required enviornment variables
__cfg__: dict[str, str | None] = {
    "ENV": env,
    "APP_DIR": APP_DIR,
    "DB_URL": DB_URL,
    "DB_URL_ASYNC": DB_URL_ASYNC,
    "DB_URL_ASYNC_IMPORTER": DB_URL_ASYNC_IMPORTER,
    "REDIS_URL": REDIS_URL,
    "MF_HISTORICAL_DATA_FOLDER": MF_HISTORICAL_DATA_FOLDER,
    "MF_IMPORTER_URL": MF_IMPORTER_URL,
    "MF_IMPORTER_TOKEN": MF_IMPORTER_TOKEN,
    "PROXY_URL": PROXY_URL,
    "PROXY_USERNAME": PROXY_USERNAME,
    "PROXY_PASSWORD": PROXY_PASSWORD,
    "DEFAULT_DATA_FOLDER": DEFAULT_DATA_FOLDER,
    "MF_INCREMENTAL_DATA_FOLDER": MF_INCREMENTAL_DATA_FOLDER,
}


# query
def cfg(key: str):
    return __cfg__.get(key) or os.getenv(key)
