"""create_scheme_details

Revision ID: c572441ebfd3
Revises: e53feb54a287
Create Date: 2025-03-09 16:10:42.133224

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "c572441ebfd3"
down_revision: Union[str, None] = "e53feb54a287"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_details",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            primary_key=True,
            nullable=False,
        ),
        sa.Column("amfi_code", sa.BigInteger(), nullable=True),
        sa.Column("cams_code", sa.String(length=50), nullable=True),
        sa.Column(
            "amc_code",
            sa.<PERSON>nteger(),
            nullable=True,
        ),
        sa.Column("s_name", sa.String(length=255), nullable=True),
        sa.Column("amfi_name", sa.String(length=500), nullable=True),
        sa.Column("isin_code", sa.String(length=50), nullable=True),
        sa.Column("type_code", sa.Integer(), nullable=True),
        sa.Column("opt_code", sa.Integer(), nullable=True),
        sa.Column("classcode", sa.Integer(), nullable=True),
        sa.Column("theme_code", sa.Integer(), nullable=True),
        sa.Column("rt_code", sa.Integer(), nullable=True),
        sa.Column("plan", sa.Integer(), nullable=True),
        sa.Column("cust_code", sa.Integer(), nullable=True),
        sa.Column("cust_code2", sa.Integer(), nullable=True),
        sa.Column("price_freq", sa.Integer(), nullable=True),
        sa.Column(
            "init_price",
            sa.Numeric(
                18,
                6,
            ),
            nullable=True,
        ),
        sa.Column("offerprice", sa.Numeric(18, 6), nullable=True),
        sa.Column("nfo_open", sa.DateTime(), nullable=True),
        sa.Column("nfo_close", sa.DateTime(), nullable=True),
        sa.Column("reopen_dt", sa.DateTime(), nullable=True),
        sa.Column("elf", sa.String(length=1), nullable=True),
        sa.Column("etf", sa.String(length=1), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.Column("stp", sa.String(length=1), nullable=True),
        sa.Column("primary_fund", sa.String(length=1), nullable=True),
        sa.Column("primary_fd_code", sa.Integer(), nullable=True),
        sa.Column("sip", sa.String(length=1), nullable=True),
        sa.Column("swp", sa.String(length=1), nullable=True),
        sa.Column("switch", sa.String(length=1), nullable=True),
        sa.Column("mininvt", sa.Numeric(18, 6), nullable=True),
        sa.Column("multiples", sa.Integer(), nullable=True),
        sa.Column("inc_invest", sa.Numeric(18, 6), nullable=True),
        sa.Column("adnmultiples", sa.Numeric(18, 6), nullable=True),
        sa.Column("fund_mgr1", sa.String(length=1000), nullable=True),
        sa.Column("fund_mgr2", sa.String(length=1000), nullable=True),
        sa.Column("fund_mgr3", sa.String(length=1000), nullable=True),
        sa.Column("fund_mgr4", sa.String(length=1000), nullable=True),
        sa.Column("since", sa.DateTime(), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=True),
        sa.Column("cutsub", sa.String(length=10), nullable=True),
        sa.Column("cutred", sa.String(length=10), nullable=True),
        sa.Column("red", sa.String(length=50), nullable=True),
        sa.Column("mob_name", sa.String(length=255), nullable=True),
        sa.Column("div_freq", sa.Integer(), nullable=True),
        sa.Column("scheme_symbol", sa.String(length=50), nullable=True),
        sa.Column("fund_mgr_code1", sa.Integer(), nullable=True),
        sa.Column("fund_mgr_code2", sa.Integer(), nullable=True),
        sa.Column("fund_mgr_code3", sa.Integer(), nullable=True),
        sa.Column("fund_mgr_code4", sa.Integer(), nullable=True),
        sa.Column("redemption_date", sa.DateTime(), nullable=True),
        sa.Column("dateofallot", sa.DateTime(), nullable=True),
        sa.Column("div_code", sa.Float(), nullable=True),
        sa.Column("legalnames", sa.String(length=255), nullable=True),
        sa.Column("amfitype", sa.String(length=50), nullable=True),
        sa.Column("nontxnday", sa.String(length=4), nullable=True),
        sa.Column("schemebank", sa.String(length=255), nullable=True),
        sa.Column("schemebankaccountnumber", sa.String(length=50), nullable=True),
        sa.Column("schemebankbranch", sa.String(length=255), nullable=True),
        sa.Column("dividendoptionflag", sa.String(length=1), nullable=True),
        sa.Column("lockin", sa.String(length=50), nullable=True),
        sa.Column("ispurchaseavailable", sa.String(length=1), nullable=True),
        sa.Column("isredeemavailable", sa.String(length=1), nullable=True),
        sa.Column("minredemptionamount", sa.Float(), nullable=True),
        sa.Column("redemptionmultipleamount", sa.Float(), nullable=True),
        sa.Column("minredemptionunits", sa.Float(), nullable=True),
        sa.Column("redemptionmultiplesunits", sa.Float(), nullable=True),
        sa.Column("minswitchamount", sa.Float(), nullable=True),
        sa.Column("switchmultipleamount", sa.Float(), nullable=True),
        sa.Column("minswitchunits", sa.Float(), nullable=True),
        sa.Column("switchmultiplesunits", sa.Float(), nullable=True),
        sa.Column("securitycode", sa.String(length=50), nullable=True),
        sa.Column("unit", sa.String(length=50), nullable=True),
        sa.Column("switchout", sa.String(length=1), nullable=True),
        sa.Column("minswitchoutamount", sa.Float(), nullable=True),
        sa.Column("switchoutmultipleamount", sa.Float(), nullable=True),
        sa.Column("minswitchoutunits", sa.Float(), nullable=True),
        sa.Column("switchoutmultiplesunits", sa.Float(), nullable=True),
        sa.Column("incept_date", sa.DateTime(), nullable=True),
        sa.Column("incept_nav", sa.Numeric(18, 6), nullable=True),
        sa.Column("defaultopt", sa.String(length=50), nullable=True),
        sa.Column("defaultplan", sa.String(length=50), nullable=True),
        sa.Column("lockperiod", sa.Integer(), nullable=True),
        sa.Column("oddraftdate", sa.DateTime(), nullable=True),
        sa.Column("liquidated_date", sa.DateTime(), nullable=True),
        sa.Column("old_plan", sa.Integer(), nullable=True),
        sa.Column("direct_plan", sa.Integer(), nullable=True),
        sa.Column("optiontype", sa.String(length=10), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_details_schemecode_idx",
        table_name="scheme_details",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_details_amc_code_idx",
        table_name="scheme_details",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_details_amfi_code_idx",
        table_name="scheme_details",
        columns=["amfi_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_details_cams_code_idx",
        table_name="scheme_details",
        columns=["cams_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_details_isin_code_idx",
        table_name="scheme_details",
        columns=["isin_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_details")
