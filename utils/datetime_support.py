from typing import Union, List
import pytz
from bisect import bisect
import datetime as _datetime
import datetime

from datetime import datetime, date, timedelta, timezone as tz
from pytz import timezone
from datetime import UTC
from dateutil import parser as dateutil_parser
from dateutil.relativedelta import relativedelta

TIMEZONE_UTC: str = "UTC"
TIMEZONE_ASIA_KOLKATA: str = "Asia/Kolkata"
asia_kolkata = timezone("Asia/Kolkata")
utc = timezone("UTC")


def utc_now() -> datetime:
    return datetime.now(tz=UTC)


def get_timezone_from_offset(dt: datetime) -> str:
    if type(dt.tzinfo) == _datetime.timezone:
        offset = dt.utcoffset()
        now = tz_now()

        for tz in pytz.all_timezones:
            _timezone = pytz.timezone(tz)
            if now.astimezone(_timezone).utcoffset() == offset:
                timezone_name = _timezone.zone
                break
    else:
        timezone_name = dt.tzinfo.zone
    return timezone_name


def parse_format(dt: str, format: str = "%Y-%m-%dT%H:%M:%S%z") -> datetime:
    dt = datetime.strptime(dt, format)  # type: ignore
    return dt  # type: ignore


def parse(dt: str) -> datetime:
    formats: List[str] = [
        "%Y-%m-%dT%H:%M:%S.%f%z",
        "%Y-%m-%dT%H:%M:%S%z",
        "%Y-%m-%dT%H:%M:%S.%f",
    ]

    for f in formats:
        try:
            parsed_datetime: datetime = parse_format(dt=dt, format=f)
            return parsed_datetime
        except Exception as e:
            pass

    raise Exception(f"Unable to parse str: {dt} to datetime")


def to_datetime(dt: Union[str, datetime]) -> datetime:
    """Converts a timezone aware ISOFormat datetime string
    or datetime object to a pytz timezone aware datetime object

    Note
        Expects timezone to be present.

    Args:
        dt (Union[str, datetime]): a ISOFormat datetime string
            ex: "2022-07-13T12:30:34.39123+05:30" or a datetime
            object

    Returns:
        datetime: A pytz timezone aware datetime object.
    """

    if type(dt) == str:
        _dt = parse(dt=dt)
        _dt = _dt.astimezone(utc)
    else:
        _dt = dt.astimezone(utc)
    return _dt


def to_pytz(dt: datetime) -> datetime:
    timezone_name = get_timezone_from_offset(dt=dt)
    return dt.astimezone(pytz.timezone(timezone_name))


def date_to_datetime(dt: date) -> datetime:
    """Accepts a date object and returns a datetime object"""
    return datetime(dt.year, dt.month, dt.day)


def time_diff(dt1: datetime, dt2: datetime) -> relativedelta:
    return relativedelta(dt1, dt2)


def to_IST(event_timestamp: datetime, tz: pytz.timezone = asia_kolkata):
    """
    >>> to_IST('2020-07-13T05:30:00.000+00:00')
    datetime.datetime(2020, 7, 13, 11, 0, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    if isinstance(event_timestamp, datetime):
        dt = event_timestamp
    else:
        dt = dateutil_parser.parse(event_timestamp)

    return dt.astimezone(tz)


def to_UTC(event_timestamp: datetime, tz: pytz.timezone = utc):
    """
    >>> to_IST('2020-07-13T05:30:00.000+00:00')
    datetime.datetime(2020, 7, 13, 11, 0, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    if isinstance(event_timestamp, datetime):
        dt = event_timestamp
    else:
        dt = dateutil_parser.parse(event_timestamp)

    return dt.astimezone(tz)


def to_UTC_without_tz(event_timestamp: str, format: str = "%Y-%m-%d %H:%M:%S.%f"):
    dt = datetime.strptime(event_timestamp, format)
    return dt.astimezone(tz.utc).strftime(format)


def beginning_of_day(dt: datetime):
    """
    >>> dt = datetime.now().replace(day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> beginning_of_day(dt)
    datetime.datetime(2020, 7, 13, 0, 0, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    dt = dt.replace(minute=0, hour=0, second=0, microsecond=0)
    return dt


def end_of_day(dt: datetime):
    """
    >>> dt = datetime.now().replace(day=13, hour=21, minute=3, second=3, microsecond=0)
    >>> end_of_day(dt.astimezone(asia_kolkata))
    datetime.datetime(2020, 7, 13, 23, 59, 59, 999999, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    dt = dt.replace(minute=59, hour=23, second=59, microsecond=999999)
    return dt


def minutes_ago(
    dt: datetime, days: int = 0, hours: int = 0, minutes: int = 1, seconds: int = 0
):
    """
    >>> dt = datetime.now().replace(day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> hours_ago(dt, minutes=1)
    datetime.datetime(2020, 7, 12, 21, 2, 3, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    past = dt - timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)
    return past


def minutes_after(
    dt: datetime, days: int = 0, hours: int = 0, minutes: int = 1, seconds: int = 0
):
    """
    >>> dt = datetime.now().replace(day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> hours_ago(dt, minutes=1)
    datetime.datetime(2020, 7, 13, 21, 4, 3, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    past = dt + timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)
    return past


def hours_ago(
    dt: datetime, days: int = 0, hours: int = 1, minutes: int = 0, seconds: int = 0
):
    """
    >>> dt = datetime.now().replace(day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> hours_ago(dt, hours=1)
    datetime.datetime(2020, 7, 12, 20, 3, 3, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    past = dt - timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)
    return past


def days_ago(
    dt: datetime, days: int = 1, hours: int = 0, minutes: int = 0, seconds: int = 0
):
    """
    >>> dt = datetime.now().replace(day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> days_ago(dt)
    datetime.datetime(2020, 7, 12, 21, 3, 3, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    past = dt - timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)
    if dt.tzinfo:
        past = past.replace(tzinfo=dt.tzinfo)
    return past


def months_ago(dt: datetime, months: int = 1):
    """
    >>> dt = datetime.now().replace(year=2020, month=7, day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> months_ago(dt, months=5)
    datetime.datetime(2020, 2, 13, 21, 3, 3, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    past = dt - relativedelta(months=months)
    return past


def months_after(dt: datetime, months: int = 1):
    """
    >>> dt = datetime.now().replace(year=2020, month=7, day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> months_ago(dt, months=5)
    datetime.datetime(2020, 2, 13, 21, 3, 3, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    past = dt + relativedelta(months=months)
    return past


def years_ago(dt: datetime, years: int = 1):
    past = dt - relativedelta(years=years)
    if dt.tzinfo:
        past = past.replace(tzinfo=past.tzinfo)
    return past


def days_after(
    dt: datetime, days: int = 1, hours: int = 0, minutes: int = 0, seconds: int = 0
):
    """
    >>> dt = datetime.now().replace(day=13, hour=21, minute=3, second=3, microsecond=0).astimezone(asia_kolkata)
    >>> days_after(dt)
    datetime.datetime(2020, 7, 14, 21, 3, 3, tzinfo=<DstTzInfo 'Asia/Kolkata' IST+5:30:00 STD>)
    """
    future = dt + timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)
    if dt.tzinfo:
        future = future.replace(tzinfo=dt.tzinfo)
    return future


def is_today(dt: datetime):
    return dt.astimezone(utc).day == datetime.now().astimezone(utc).day


def is_yesterday(dt: datetime):
    return dt.astimezone(utc).day == days_ago(datetime.now().astimezone(utc)).day


def is_tomorrow(dt: datetime):
    return dt.astimezone(utc).day == days_after(datetime.now().astimezone(utc)).day


def IST_time():
    return datetime.now().astimezone(asia_kolkata)


def tz_now(tz: pytz = asia_kolkata):
    dt = datetime.now(tz=tz)
    return dt


def from_iso(dt: str, format="%Y-%m-%dT%H:%M:%S.%f%z") -> datetime:
    return datetime.strptime(dt, format)


def tz_from_iso(
    dt: str, to_tz: pytz = utc, format="%Y-%m-%dT%H:%M:%S.%f%z"
) -> datetime:
    date_time = parse(dt=dt)
    return date_time.astimezone(to_tz)


def get_bucket(local_event_timestamp: datetime):
    time_buckets = get_time_buckets()
    bisect_idx = bisect(time_buckets, local_event_timestamp) - 1
    bucket = time_buckets[bisect_idx]
    bucket_str = str(bucket.to_pydatetime())
    return bucket_str


def start_of_week(dt: str, to_tz: pytz = utc) -> datetime:
    day_of_the_week = dt.weekday()
    return days_ago(dt=dt, days=day_of_the_week)


def end_of_week(dt: str, to_tz: pytz = utc) -> datetime:
    _start_of_week = start_of_week(dt=dt, to_tz=to_tz)
    return days_after(dt=_start_of_week, days=6)


def end_of_last_week(dt: str, to_tz: pytz = utc):
    _end_of_current_week = end_of_week(dt=dt, to_tz=to_tz)
    return days_ago(dt=_end_of_current_week, days=7)


def start_of_previous_week(dt: str, to_tz: pytz = utc):
    _start_of_week = start_of_week(dt=dt, to_tz=to_tz)
    return days_ago(dt=_start_of_week, days=7)


class InvalidDateRange(Exception):
    pass


class DateRangeTooSmall(Exception):
    pass


# if __name__ == '__main__':
#     dt = tz_now().isoformat()
#     dt = from_iso(dt=dt)
#     get_timezone_from_offset(dt=dt)
