"""create_mf_sip

Revision ID: 4764683b42e3
Revises: 1b01a7e5f790
Create Date: 2025-03-09 17:04:02.830661

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "4764683b42e3"
down_revision: Union[str, None] = "1b01a7e5f790"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "mf_sip",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column(
            "amc_code",
            sa.BigInteger(),
            nullable=True,
        ),
        sa.Column("frequency", sa.String(length=100), nullable=False),
        sa.Column("sip", sa.String(length=1), nullable=True),
        sa.Column("sipdatescondition", sa.String(length=8000), nullable=True),
        sa.Column("dates", sa.String(length=8000), nullable=True),
        sa.Column("sipdaysall", sa.String(length=50), nullable=True),
        sa.Column("sipmininvest", sa.Numeric(18, 6), nullable=True),
        sa.Column("sipaddninvest", sa.Numeric(18, 6), nullable=True),
        sa.Column("sipfrequencyno", sa.Integer(), nullable=True),
        sa.Column("sipminimumperiod", sa.Integer(), nullable=True),
        sa.Column("sipmaximumperiod", sa.String(length=100), nullable=True),
        sa.Column("sipmincumamount", sa.String(length=100), nullable=True),
        sa.Column("sipminunits", sa.Numeric(18, 6), nullable=True),
        sa.Column("sipmultiplesunits", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "frequency"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="mf_sip_schemecode_idx",
        table_name="mf_sip",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_sip_amc_code_idx",
        table_name="mf_sip",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("mf_sip")
