"""create_mf_ratio

Revision ID: e3f1a3ba2a18
Revises: 9a145dc7a64d
Create Date: 2025-04-27 11:32:30.507431

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e3f1a3ba2a18"
down_revision: Union[str, None] = "9a145dc7a64d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the mf_ratio table based on the provided schema.
    This table contains scheme ratios information (1 Year ratios calculated daily).
    """

    op.create_table(
        "mf_ratio",  # Table name in lowercase as requested
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Scheme Code",
        ),  # Using BigInteger as requested for Integer from source [12]
        sa.Column(
            "upddate", sa.DateTime, nullable=True, comment="Updation Date"
        ),  # Datetime from source [12]
        sa.Column(
            "datefrom",
            sa.DateTime,
            nullable=True,
            comment="Ratio calculation ‘from’ date range",
        ),  # Datetime from source [12]
        sa.Column(
            "dateto",
            sa.DateTime,
            nullable=True,
            comment="Ratio calculation ‘to’ date range",
        ),  # Datetime from source [12]
        sa.Column(
            "avg_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Average Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [12], lowercase
        sa.Column(
            "avg_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Average Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [12], lowercase
        sa.Column(
            "sd_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Standard Deviation of the benchmark",
        ),  # Using Numeric(18,6) for Float from source [12], lowercase
        sa.Column(
            "sd_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Standard Deviation of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "jalpha_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Jensen's Alpha of the benchmark",
        ),  # Using Numeric(18,6) for Float from source [12], corrected comment
        sa.Column(
            "jalpha_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Jensen's Alpha of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], corrected comment
        sa.Column(
            "semisd_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Semi Standard Deviation of the benchmark",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "semisd_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Semi Standard Deviation of Scheme",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "beta_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Beta of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "beta_y", sa.Numeric(18, 6), nullable=True, comment="Beta of the Scheme"
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "corelation_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Corelation of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "corelation_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Corelation of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "betacor_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Beta Corelation of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "betacor_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Beta Corelation of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "treynor_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Treynor Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "treynor_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Treynor Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "fama_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="FAMA Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "fama_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="FAMA Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], lowercase
        sa.Column(
            "sharpe_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Sharpe Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [13], added missing column
        sa.Column(
            "sharpe_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Sharpe Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], added missing column
        sa.Column(
            "sortino_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Sortino Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [13], added missing column
        sa.Column(
            "sortino_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Sortino Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [13], added missing column
        sa.Column(
            "flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"
        ),  # Varchar(1) from source [4]
        # Primary key is 'schemecode' based on the source [12]
        sa.PrimaryKeyConstraint("schemecode"),
    )


def downgrade():
    """
    Drops the mf_ratio table.
    """
    op.drop_table("mf_ratio")
