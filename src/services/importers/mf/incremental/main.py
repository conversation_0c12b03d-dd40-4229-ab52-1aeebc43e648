from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from config.logger import log_info, log_error
from utils.datetime_support import asia_kolkata
from connectors.async_pg import get_async_session, AsyncSession
from src.services.importers.mf.incremental.importer_service import (
    IncrementalImporterService,
)
from src.services.importers.mf.incremental.csv_converter_service import (
    CSVConverterService,
)
from src.services.importers.mf.incremental.db_write_service import DBWriteService


class IncrementalImportLauncher:
    @staticmethod
    async def run(
        current_date: Optional[str] = None, db_session: Optional[AsyncSession] = None
    ):
        """Launches the incremental import process."""
        if current_date is None:
            current_date = datetime.now().astimezone(asia_kolkata).strftime("%d-%m-%Y")
        else:
            current_date = datetime.strptime(current_date, "%d-%m-%Y").strftime(
                "%d-%m-%Y"
            )

        if not db_session:
            db_session = await get_async_session()
        log_info(msg=f"Starting incremental import process for date: {current_date}")
        await IncrementalImporterService.run(current_date=current_date)
        await CSVConverterService.run(current_date=current_date)
        await DBWriteService.run(current_date=current_date, db_session=db_session)
        log_info(msg=f"Completed incremental import process for date: {current_date}")

        # Fetch import file list


if __name__ == "__main__":
    import asyncio

    # Example usage
    async def main():
        start_date = datetime(2025, 6, 23)
        end_date = datetime(2025, 6, 29)

        current_dt = start_date
        while current_dt <= end_date:
            db_session: AsyncSession = await get_async_session()
            current_date_str = current_dt.strftime("%d-%m-%Y")
            await IncrementalImportLauncher.run(
                current_date=current_date_str, db_session=db_session
            )
            current_dt += timedelta(days=1)
            await db_session.close()

    asyncio.run(main())
