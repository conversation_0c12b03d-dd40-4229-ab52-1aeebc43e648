"""create_amc_aum

Revision ID: 9b100bbd7b48
Revises: 8ec595348996
Create Date: 2025-03-09 17:42:52.257088

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "9b100bbd7b48"
down_revision: Union[str, None] = "8ec595348996"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "amc_aum",
        sa.Column("amc_code", sa.BigInteger(), nullable=False),
        sa.Column("aumdate", sa.DateTime(), nullable=False),
        sa.Column("totalaum", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.<PERSON>KeyConstraint("amc_code", "aumdate"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="amc_aum_amc_code_idx",
        table_name="amc_aum",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("amc_aum")
