"""fundmanager_mst

Revision ID: a461bd8a6b4f
Revises: a54375b0404a
Create Date: 2025-03-09 16:55:44.723669

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "a461bd8a6b4f"
down_revision: Union[str, None] = "a54375b0404a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "fundmanager_mst",
        sa.Column("id", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("initial", sa.String(length=10), nullable=True),
        sa.Column("fundmanager", sa.String(length=200), nullable=True),
        sa.Column("qualification", sa.String(length=200), nullable=True),
        sa.Column("experience", sa.String(length=200), nullable=True),
        sa.Column("basicdetails", sa.String(length=1000), nullable=True),
        sa.Column("designation", sa.String(length=100), nullable=True),
        sa.Column("age", sa.Integer(), nullable=True),
        sa.Column("reporteddate", sa.DateTime(), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="fundmanager_mst_id_idx",
        table_name="fundmanager_mst",
        columns=["id"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="fundmanager_mst_fundmanager_idx",
        table_name="fundmanager_mst",
        columns=["fundmanager"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("fundmanager_mst")
