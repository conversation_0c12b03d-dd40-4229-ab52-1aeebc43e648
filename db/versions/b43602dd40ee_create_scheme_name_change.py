"""create_scheme_name_change

Revision ID: b43602dd40ee
Revises: fb995d35f0a1
Create Date: 2025-04-27 11:41:36.010193

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b43602dd40ee"
down_revision: Union[str, None] = "fb995d35f0a1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_name_change",
        sa.Column(
            "amc_code", sa.BigInteger(), nullable=False
        ),  # Integer -> BigInteger [7]
        sa.Column(
            "schemecode", sa.BigInteger(), nullable=False
        ),  # Integer -> BigInteger [7]
        sa.Column("effectivedate", sa.DateTime(), nullable=False),  # Datetime [7]
        sa.Column("oldname", sa.String(length=255), nullable=True),  # Varchar(255) [7]
        sa.Column("newname", sa.String(length=255), nullable=True),  # Varchar(255) [7]
        sa.Column("flag", sa.String(length=1), nullable=True),  # Varchar(1) [7]
        # Composite Primary Key based on Source [7]
        sa.PrimaryKeyConstraint("amc_code", "schemecode", "effectivedate"),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_name_change")
