"""create_rt_mst

Revision ID: cc980b69beb5
Revises: 88b0b8c6cb37
Create Date: 2025-03-09 16:47:44.631638

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "cc980b69beb5"
down_revision: Union[str, None] = "88b0b8c6cb37"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "rt_mst",
        sa.Column("rt_code", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("rt_name", sa.String(length=100), nullable=True),
        sa.Column("sebi_reg_no", sa.String(length=50), nullable=True),
        sa.Column("address1", sa.String(length=4000), nullable=True),
        sa.Column("address2", sa.String(length=4000), nullable=True),
        sa.Column("address3", sa.String(length=4000), nullable=True),
        sa.Column("state", sa.String(length=50), nullable=True),
        sa.Column("tel", sa.String(length=4000), nullable=True),
        sa.Column("fax", sa.String(length=4000), nullable=True),
        sa.Column("website", sa.String(length=100), nullable=True),
        sa.Column("reg_address", sa.String(length=4000), nullable=True),
        sa.Column("email", sa.String(length=500), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="rt_mst_rt_code_idx",
        table_name="rt_mst",
        columns=["rt_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="rt_mst_rt_name_idx",
        table_name="rt_mst",
        columns=["rt_name"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("rt_mst")
