"""create_scheme_paum

Revision ID: 8ec595348996
Revises: ff4656e309fe
Create Date: 2025-03-09 17:40:47.002436

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "8ec595348996"
down_revision: Union[str, None] = "ff4656e309fe"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_paum",
        sa.Column("schemecode", sa.BigInteger(), nullable=False),
        sa.<PERSON>umn("monthend", sa.Integer(), nullable=False),
        sa.<PERSON>umn("amc_code", sa.BigInteger(), nullable=True),
        sa.Column("aum", sa.Numeric(18, 6), nullable=True),
        sa.<PERSON>umn("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "monthend"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_paum_schemecode_idx",
        table_name="scheme_paum",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_paum_amc_code_idx",
        table_name="scheme_paum",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_paum")
