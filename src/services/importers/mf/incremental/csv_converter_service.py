import os
import json
import csv
from config.cfg import cfg
from os import Path<PERSON><PERSON>
from typing import List, Dict, Any, Optional, Type
from config.logger import log_info, log_error
from utils.datetime_support import asia_kolkata
from datetime import datetime, date


class TableService:
    """Service to read a JSON file and convert it to a CSV file.
    It also provides methods to get the table data and column names.
    The JSON file is expected to be in the format:
    {
        "Table": [
            {
                "Column1": "Value1",
                "Column2": "Value2",
                ...
            },
            ...
        ]
    }
    The CSV file will be saved in the same folder as the JSON file with the same name.
    The CSV file will have the same column names as the JSON file.
    The CSV file will be saved in the folder specified by the folder_name parameter.
    The folder_name parameter is expected to be a path-like object.
    The CSV file will be saved in the folder_name/csv folder.
    The CSV file will be named <database_table_name>.csv where database_table_name is the name of the JSON file without the .txt extension.
    """

    def __init__(self, filename: <PERSON><PERSON><PERSON>, folder_name: <PERSON><PERSON>ike) -> None:
        self.filename: PathLike = filename
        self.database_table_name: str = (
            str(filename).lower().replace(".json", "").split("/")[-1]
        )
        self.csv_folder_name: str = f"{folder_name}/csv"
        self.csv_file_path: PathLike = os.path.join(
            self.csv_folder_name, f"{self.database_table_name}.csv"
        )
        self.table_data: List[Dict[str, Any]] = []
        self.column_names: List[str] = []

    async def __call__(self):
        await self.invoke()

    async def invoke(self):
        await self.get_table_data()
        await self.get_column_names()

    async def get_table_data(self) -> List[Dict[str, Any]]:
        """Get the table data from the JSON file.
        The JSON file is expected to be in the format:
        {
            "Table": [
                {
                    "Column1": "Value1",
                    "Column2": "Value2",
                    ...
                },
                ...
            ]
        }

        Returns:
            List[Dict[str, Any]]: The table data as a list of dictionaries.
        """
        with open(self.filename, "r") as f:
            data: Dict[str, Any] = json.load(f)
            self.table_data: List[Dict[str, Any]] = data.get("Table", [])
            for index, row in enumerate(self.table_data):
                self.table_data[index] = {k.lower(): v for k, v in row.items()}
        return self.table_data

    async def get_column_names(self) -> List[str]:
        """Get the column names from the table data.
        The column names are the keys of the first row of the table data.

        Returns:
            List[str]: The column names as a list of strings.
        If the table data is empty, an empty list is returned.
        If the table data is not empty, the column names are sorted and returned.
        """
        if not self.table_data:
            await self.get_table_data()

        first_row: Optional[Dict[str, Any]] = self.table_data[0]

        if not first_row:
            self.column_names = []
        self.column_names = [cn.lower() for cn in sorted(list(first_row.keys()))]
        return self.column_names


class CSVConverterService:
    @staticmethod
    async def run(current_date: Optional[str] = None):
        """Run the CSVConverterService to convert JSON files to CSV files.
        This method initializes the CSVConverterService with the JSON folder path
        and invokes the conversion process.
        If no current_date is provided, it defaults to the current date in Asia/Kolkata timezone.
        The JSON folder path is retrieved from the configuration using the key "MF_INCREMENTAL_DATA_FOLDER".
        The JSON files are expected to be in the format:

        Args:
            current_date (Optional[str], optional): The date for which to convert JSON files to CSV files in 'dd-mm-yyyy' format.
            Defaults to None, which uses the current date.
        """
        if not current_date:
            current_date: date = datetime.now().astimezone(asia_kolkata).date()
        else:
            current_date: date = datetime.strptime(current_date, "%d-%m-%Y").date()
        current_date_str: str = current_date.strftime("%d%m%Y")
        json_folder_path: PathLike = cfg("MF_INCREMENTAL_DATA_FOLDER")
        folder_path: PathLike = os.path.join(json_folder_path, current_date_str)
        converter_service: CSVConverterService = CSVConverterService(
            json_folder=folder_path,
            table_service=TableService,
        )
        await converter_service.invoke()

    def __init__(
        self,
        json_folder: PathLike,
        csv_folder: Optional[PathLike] = None,
        table_service: Optional[TableService] = None,
    ) -> None:
        self.json_folder: PathLike = json_folder
        self.csv_folder: PathLike = (
            os.path.join(self.json_folder, "csv") if csv_folder is None else csv_folder
        )
        self.table_service: Optional[TableService] = table_service

    async def __call__(self):
        await self.invoke()

    async def initialize_table_service(self, json_file_path: PathLike):
        """Initialize the TableService with the given JSON file path.
        This method creates an instance of TableService with the provided JSON file path
        and the folder name where the CSV files will be saved.

        Args:
            json_file_path (PathLike): The path to the JSON file to be processed.

        Returns:
            _type_: _description_
        """

        self.table_service = TableService(
            filename=json_file_path, folder_name=self.json_folder
        )
        return self.table_service

    async def invoke(self):
        """Invoke the CSVConverterService to convert JSON files to CSV files."""

        await self.create_csv_folder_if_not_exists()
        for entry in sorted(os.scandir(self.json_folder), key=lambda e: e.name):
            if entry.is_file() and entry.name.endswith(".json"):  #
                json_file_path: str = entry.path
                await self.initialize_table_service(json_file_path)
                table_svc: TableService = self.table_service
                await table_svc.invoke()
                self.csv_folder = table_svc.csv_folder_name
                table_name: str = table_svc.database_table_name
                table_data: List[Dict[str, Any]] = table_svc.table_data
                if not table_data:
                    continue
                with open(
                    table_svc.csv_file_path,
                    "w",
                    newline="",
                    encoding="utf-8",
                ) as csvfile:
                    writer = csv.DictWriter(
                        csvfile,
                        fieldnames=table_svc.column_names,
                        quoting=csv.QUOTE_MINIMAL,
                    )
                    writer.writeheader()
                    writer.writerows(table_data)
                log_info(
                    msg=f"JSON filename: {entry.name}, table_name: {table_name}, csv_file_path: {table_svc.csv_file_path}"
                )

    async def create_csv_folder_if_not_exists(self):
        """Create the CSV folder if it does not exist."""
        if not os.path.exists(self.csv_folder):
            os.makedirs(self.csv_folder)
            log_info(msg=f"Created CSV folder: {self.csv_folder}")
        else:
            log_info(msg=f"CSV folder already exists: {self.csv_folder}")


if __name__ == "__main__":
    import asyncio
    import os

    async def main():
        json_folder_path: PathLike = cfg("MF_INCREMENTAL_DATA_FOLDER")
        for folder in sorted(os.listdir(json_folder_path)):
            if folder.startswith("03062025") or folder.startswith("."):
                continue

            folder_path: PathLike = os.path.join(json_folder_path, folder)
            converter_service: CSVConverterService = CSVConverterService(
                json_folder=folder_path,
                table_service=TableService,
            )
            await converter_service.invoke()

    asyncio.run(main())
