import asyncio
from datetime import datetime
from utils.datetime_support import asia_kolkata
from config.logger import log_error, log_info
from celery.utils.log import get_task_logger
from connectors.async_pg import get_async_sessionmaker, AsyncSession
from src.models import model
from src.tasks.worker import celery
from src.services.importers.mf.incremental.main import IncrementalImportLauncher

logger = get_task_logger(__name__)


async def incremental_import_worker_async(task_type):
    async with get_async_sessionmaker() as session:
        try:
            await IncrementalImportLauncher.run(current_date=None, db_session=session)

        except Exception as e:
            log_error(
                f"[IncrementalImportWorker] Incremental import launcher failed on {str(datetime.now().astimezone(asia_kolkata))}. Error: {e}"
            )

    return True


@celery.task(name="incremental_import_worker", bind=True)
def incremental_import_worker(task_type):
    log_info(f"Incremental import launcher started at {datetime.now().isoformat()}.")
    loop = asyncio.get_event_loop()
    loop.run_until_complete(incremental_import_worker_async(task_type=task_type))
    return True
