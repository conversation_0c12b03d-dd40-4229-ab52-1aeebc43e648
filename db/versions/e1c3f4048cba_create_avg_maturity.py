"""create_avg_maturity

Revision ID: e1c3f4048cba
Revises: e606f7586a6e
Create Date: 2025-04-07 12:34:31.433436

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e1c3f4048cba"
down_revision: Union[str, None] = "e606f7586a6e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""

    op.create_table(
        "avg_maturity",
        sa.Column("amc_code", sa.BigInteger()),
        sa.Column("schemecode", sa.<PERSON>ger(), primary_key=True),
        sa.Column("date", sa.DateTime()),
        sa.Column("invenddate", sa.DateTime()),
        sa.Column("avg_mat_num", sa.Nume<PERSON>(19, 6)),
        sa.Column("avg_mat_days", sa.String(length=25)),
        sa.<PERSON>umn("mod_dur_num", sa.Numeric(19, 6)),
        sa.Column("mod_dur_days", sa.String(length=25)),
        sa.Column("ytm", sa.Numeric(19, 6)),  # Yield TO Maturity
        sa.Column("turnover_ratio", sa.Numeric(19, 6)),
        sa.Column("tr_mode", sa.String(20)),
        sa.Column("flag", sa.String(2)),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="idx_amc_code_avg_maturity",
        table_name="avg_maturity",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="idx_schemecode_avg_maturity",
        table_name="avg_maturity",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="idx_amc_code_schemecode_avg_maturity",
        table_name="avg_maturity",
        columns=["amc_code", "schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("avg_maturity")
