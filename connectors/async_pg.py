import sqlalchemy
from sqlalchemy.pool import NullPool
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    create_async_engine,
    async_scoped_session,
    AsyncEngine,
)
from sqlalchemy.orm import sessionmaker
from config.cfg import cfg
from sqlalchemy.dialects.postgresql import TSVECTOR
from contextlib import asynccontextmanager


class TSVector(sqlalchemy.types.TypeDecorator):
    impl = TSVECTOR
    cache_ok = True


metadata = sqlalchemy.MetaData()


pool_size: int = 8 if cfg("ENV") == "staging" else 10
max_overflow: int = 2 if cfg("ENV") == "staging" else 10

async_engine: AsyncEngine = create_async_engine(
    cfg("DB_URL_ASYNC"),
    pool_size=pool_size,
    echo=False,
    max_overflow=max_overflow,
    pool_use_lifo=True,
    pool_recycle=1300,
    echo_pool=True,
)
AsyncSessionLocal: AsyncSession = sessionmaker(  # type: ignore
    bind=async_engine,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
    class_=AsyncSession,
)


async def get_async_session() -> AsyncSession:
    """
    Get an asynchronous session for interacting with the database.

    Returns:
        AsyncSession: The asynchronous session object.
    """
    session: AsyncSession = AsyncSessionLocal()  # type: ignore
    return session


@asynccontextmanager
async def get_async_sessionmaker():
    """
    Context manager that provides an asynchronous session for database operations.

    Usage:
    async with get_async_sessionmaker() as session:
        # Perform database operations using the session

    """
    session: AsyncSession = AsyncSessionLocal()  # type: ignore
    try:
        yield session
    finally:
        if session.is_active:
            await session.close()
