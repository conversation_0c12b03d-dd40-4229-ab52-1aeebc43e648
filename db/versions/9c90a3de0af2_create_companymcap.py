"""create_companymcap

Revision ID: 9c90a3de0af2
Revises: 1c623c9943fd
Create Date: 2025-04-27 10:56:06.087464

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "9c90a3de0af2"
down_revision: Union[str, None] = "1c623c9943fd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    Creates the CompanyMcap table based on the provided schema.
    This table contains market capitalization type information for companies.
    Data is updated bi-annually and is maintained for primary schemes.
    """
    op.create_table(
        "companymcap",
        sa.Column(
            "fincode", sa.BigInteger(), primary_key=True, comment="Accord company code"
        ),  # [4]
        sa.Column("mcap", sa.Numeric(18, 6), nullable=True, comment="Mcap"),  # [4]
        sa.Column("mode", sa.VARCHAR(20), nullable=True, comment="Mcap type"),  # [4]
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),  # [4]
        # Primary key is 'fincode' based on the source [4]
        sa.PrimaryKeyConstraint("fincode"),
    )


def downgrade():
    """
    Drops the CompanyMcap table.
    """
    op.drop_table("companymcap")
