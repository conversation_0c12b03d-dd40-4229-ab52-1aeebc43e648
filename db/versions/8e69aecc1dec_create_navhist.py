"""create_navhist

Revision ID: 8e69aecc1dec
Revises: 1e50c2f43fb8
Create Date: 2025-03-09 17:51:24.985181

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "8e69aecc1dec"
down_revision: Union[str, None] = "1e50c2f43fb8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "navhist",
        sa.Column("schemecode", sa.BigInteger(), nullable=False),
        sa.Column("navdate", sa.DateTime(), nullable=False),
        sa.Column("navrs", sa.Numeric(18, 6), nullable=True),
        sa.Column("repurprice", sa.Numeric(18, 6), nullable=True),
        sa.Column("saleprice", sa.Numeric(18, 6), nullable=True),
        sa.Column("adjustednav_c", sa.Numeric(18, 6), nullable=True),
        sa.Column("adjustednav_nonc", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "navdate"),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("navhist")
