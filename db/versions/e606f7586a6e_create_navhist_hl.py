"""create_navhist_hl

Revision ID: e606f7586a6e
Revises: 8e69aecc1dec
Create Date: 2025-03-09 17:52:37.649430

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e606f7586a6e"
down_revision: Union[str, None] = "8e69aecc1dec"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "navhist_hl",
        sa.Column("schemecode", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("3monthhhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("3monthlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("3mhdate", sa.DateTime(), nullable=True),
        sa.Column("3mldate", sa.DateTime(), nullable=True),
        sa.Column("6monthhhigh", sa.<PERSON>ume<PERSON>(18, 6), nullable=True),
        sa.Column("6monthlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("6mhdate", sa.DateTime(), nullable=True),
        sa.Column("6mldate", sa.DateTime(), nullable=True),
        sa.Column("9monthhhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("9monthlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("9mhdate", sa.DateTime(), nullable=True),
        sa.Column("9mldate", sa.DateTime(), nullable=True),
        sa.Column("52weekhhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("52weeklow", sa.Numeric(18, 6), nullable=True),
        sa.Column("52whdate", sa.DateTime(), nullable=True),
        sa.Column("52wldate", sa.DateTime(), nullable=True),
        sa.Column("1yrhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("1yrlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("1yhdate", sa.DateTime(), nullable=True),
        sa.Column("1yldate", sa.DateTime(), nullable=True),
        sa.Column("2yrhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("2yrlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("2yhdate", sa.DateTime(), nullable=True),
        sa.Column("2yldate", sa.DateTime(), nullable=True),
        sa.Column("3yrhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("3yrlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("3yhdate", sa.DateTime(), nullable=True),
        sa.Column("3yldate", sa.DateTime(), nullable=True),
        sa.Column("5yrhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("5yrlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("5yhdate", sa.DateTime(), nullable=True),
        sa.Column("5yldate", sa.DateTime(), nullable=True),
        sa.Column("ytdhigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("ytdlow", sa.Numeric(18, 6), nullable=True),
        sa.Column("ytdhdate", sa.DateTime(), nullable=True),
        sa.Column("ytdldate", sa.DateTime(), nullable=True),
        sa.Column("sihigh", sa.Numeric(18, 6), nullable=True),
        sa.Column("silow", sa.Numeric(18, 6), nullable=True),
        sa.Column("sihdate", sa.DateTime(), nullable=True),
        sa.Column("sildate", sa.DateTime(), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="navhist_hl_schemecode_idx",
        table_name="navhist_hl",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("navhist_hl")
