"""schemeload

Revision ID: f48495fe615d
Revises: db7167f4111e
Create Date: 2025-03-09 17:23:53.228950

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "f48495fe615d"
down_revision: Union[str, None] = "db7167f4111e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "schemeload",
        sa.Column(
            "schemecode",
            sa.<PERSON>nteger(),
            nullable=False,
        ),
        sa.Column("ldate", sa.DateTime(), nullable=False),
        sa.Column("ltypecode", sa.BigInteger(), nullable=False),
        sa.Column("lsrno", sa.<PERSON>Integer(), nullable=False),
        sa.Column("frmamount", sa.Numeric(18, 6), nullable=True),
        sa.Column("uptoamount", sa.Numeric(18, 6), nullable=True),
        sa.Column("minperiod", sa.Integer(), nullable=True),
        sa.Column("maxperiod", sa.Integer(), nullable=True),
        sa.Column("min", sa.String(length=10), nullable=True),
        sa.Column("max", sa.String(length=10), nullable=True),
        sa.Column("entryload", sa.Numeric(18, 6), nullable=True),
        sa.Column("exitload", sa.Numeric(18, 6), nullable=True),
        sa.Column("remarks", sa.Text(), nullable=True),  # ntext -> Text
        sa.Column("period_condition", sa.String(length=10), nullable=True),
        sa.Column("period_type", sa.String(length=10), nullable=True),
        sa.Column("period", sa.String(length=100), nullable=True),
        sa.Column("amount_condition", sa.String(length=10), nullable=True),
        sa.Column("amount_type", sa.String(length=10), nullable=True),
        sa.Column("per_condition", sa.String(length=10), nullable=True),
        sa.Column("per_frm", sa.Numeric(18, 6), nullable=True),
        sa.Column("per_to", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "ldate", "ltypecode", "lsrno"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="schemeload_schemecode_idx",
        table_name="schemeload",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="schemeload_ltypecode_idx",
        table_name="schemeload",
        columns=["ltypecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="schemeload_lsrno_idx",
        table_name="schemeload",
        columns=["lsrno"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("schemeload")
