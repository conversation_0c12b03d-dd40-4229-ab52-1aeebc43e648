"""create_scheme_rtcode

Revision ID: 295ac8345827
Revises: c572441ebfd3
Create Date: 2025-03-09 16:20:53.020068

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "295ac8345827"
down_revision: Union[str, None] = "c572441ebfd3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_rtcode",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            primary_key=True,
            nullable=False,
        ),
        sa.Column("rtschemecode", sa.String(length=100), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_rtcode_schemecode_idx",
        table_name="scheme_rtcode",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_rtcode_rtschemecode_idx",
        table_name="scheme_rtcode",
        columns=["rtschemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_rtcode")
