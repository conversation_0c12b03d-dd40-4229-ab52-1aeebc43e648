import asyncio
import os
import json
from os import PathLike
from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime, date, timedelta
from utils.datetime_support import asia_kolkata
from config.cfg import cfg
from config.logger import log_info, log_error

from src.services.importers.mf.incremental.client import ImporterClient


class ImportFileInfo(BaseModel):
    filename: str
    section: str
    sub: str = ""
    current_date: str = Field(
        default_factory=lambda: datetime.now().date().strftime("%d%m%Y")
    )


class IncrementalImporterService:
    @staticmethod
    async def run(current_date: Optional[str] = None):
        """Runs the incremental importer service to fetch and process files for a given date.
        This method initializes the service, sets the current date, creates necessary folders,
        and fetches the required files from the MF Importer service.
        If no date is provided, it defaults to the current date in the format DD-MM-YYYY.

        Args:
            current_date (Optional[str], optional): The date for which to fetch files, in the format DD-MM-YYYY.

        Returns:
            _type_: Returns an instance of IncrementalImporterService after processing the files.
        """
        if not current_date:
            current_date: str = datetime.now().astimezone(asia_kolkata).date()
        else:
            current_date: date = datetime.strptime(current_date, "%d-%m-%Y").date()
        current_date_str: str = current_date.strftime("%d-%m-%Y")
        service: IncrementalImporterService = IncrementalImporterService()
        await service.invoke(current_date=current_date_str)
        return service

    def __init__(self):
        self.proxy_url: str = cfg("PROXY_URL")
        self.proxy_username: str = cfg("PROXY_USERNAME")
        self.proxy_password: str = cfg("PROXY_PASSWORD")
        self.default_data_folder: str = cfg("DEFAULT_DATA_FOLDER")
        self.current_date: date = datetime.now().astimezone(asia_kolkata).date()
        self.current_date_import_folder: str = os.path.join(
            self.default_data_folder,
            "incremental",
            datetime.now().date().strftime("%d%m%Y"),
        )

        self.import_file_list: list[str] = [
            {
                "filename": "Scheme_master",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Scheme_details",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Scheme_rtcode",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "schemeisinmaster",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Type_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Sclass_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "RT_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Plan_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Cust_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Fundmanager_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Div_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Scheme_objective",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "MF_sip",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Scheme_index",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Index_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Schemeload",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Companymaster",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Industry_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Asect_mst",
                "section": "MFMaster",
                "sub": "",
            },
            {
                "filename": "Mf_portfolio",
                "section": "MfPortfolio",
                "sub": "",
            },
            {
                "filename": "Amc_paum",
                "section": "MfPortfolio",
                "sub": "",
            },
            {
                "filename": "Scheme_paum",
                "section": "MfPortfolio",
                "sub": "",
            },
            {
                "filename": "Portfolio_inout",
                "section": "MfPortfolio",
                "sub": "",
            },
            {
                "filename": "Avg_scheme_aum",
                "section": "MfPortfolio",
                "sub": "",
            },
            {
                "filename": "Currentnav",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Navhist",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Mf_return",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Navhist_hl",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Mf_abs_return",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Mf_cagr_return",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "ClassWiseReturn",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "BM_AbsoluteReturn",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "BM_AnnualisedReturn",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Mf_ratio",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "MF_Ratios_DefaultBM",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Ratio_3Year_MonthlyRet",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Divdetails",
                "section": "MFNav",
                "sub": "",
            },
            {
                "filename": "Expenceratio",
                "section": "MFOther",
                "sub": "",
            },
            {
                "filename": "Scheme_eq_details",
                "section": "MFOther",
                "sub": "",
            },
            {
                "filename": "Scheme_Name_Change",
                "section": "MFOther",
                "sub": "",
            },
            {
                "filename": "DailyFundmanager",
                "section": "MFOther",
                "sub": "",
            },
            {
                "filename": "Mergedschemes",
                "section": "MFOther",
                "sub": "",
            },
            {
                "filename": "CompanyMcap",
                "section": "MFOther",
                "sub": "",
            },
        ]

    async def invoke(self, current_date: Optional[str] = None):
        """Invokes the incremental importer service to fetch and process files for a given date.

        Args:
            current_date (Optional[str], optional): The date for which to fetch files, in the format DD-MM-YYYY.
        If no date is provided, it defaults to the current date in the format DD-MM-YYYY.

        Raises:
            ValueError: If the provided current date is not in the format DD-MM-YYYY.
        """
        if current_date:
            await self.set_current_date(current_date=current_date)
            await self.update_current_date_import_folder(current_date=current_date)
        await self.create_folder_if_not_exists()
        await self.fetch_files()

    async def set_current_date(self, current_date: str):
        """Sets the current date for the service.

        Args:
            current_date (str): The date to set, in the format DD-MM-YYYY.

        Raises:
            ValueError: If the provided current date is not in the format DD-MM-YYYY.
        """
        try:
            self.current_date = datetime.strptime(current_date, "%d-%m-%Y").date()
        except ValueError:
            raise ValueError("Current date must be in the format DDMMYYYY.")

    async def update_current_date_import_folder(self, current_date: str):
        """Updates the current date import folder based on the provided date.

        Args:
            current_date (str): The date to set, in the format DD-MM-YYYY.

        Raises:
            ValueError: If the provided current date is not in the format DD-MM-YYYY.
        """
        try:
            current_date_str: str = self.current_date.strftime("%d%m%Y")
            self.current_date_import_folder = os.path.join(
                self.default_data_folder, "incremental", current_date_str
            )
        except ValueError:
            raise ValueError("Current date must be in the format DDMMYYYY.")

    async def create_folder_if_not_exists(self):
        """Creates the current date import folder if it does not exist."""
        if not os.path.exists(self.current_date_import_folder):
            os.makedirs(self.current_date_import_folder)
            log_info(msg=f"Created folder: {self.current_date_import_folder}")
        else:
            log_info(msg=f"Folder already exists: {self.current_date_import_folder}")

    async def fetch_files(self):
        """Fetches the import files for the current date from the MF Importer service."""
        current_date_str: str = self.current_date.strftime("%d%m%Y")
        for file_info in self.import_file_list:
            import_file: ImportFileInfo = ImportFileInfo.model_validate(file_info)
            import_file.current_date = current_date_str
            result: Optional[dict] = await ImporterClient.get(
                import_file_info=import_file, with_proxy=True
            )
            log_info(
                msg=f"Fetched {import_file.filename} for date {self.current_date}: Fetch completed"
            )
            if not result:
                continue

            json_file_path: str = await self.write_file(
                filename=import_file.filename,
                data=result,
            )

    async def write_file(self, filename: str, data: dict):
        """Writes the provided data to a JSON file in the current date import folder.

        Args:
            filename (str): The name of the file to write (without extension).
            data (dict): The data to write to the file in JSON format.

        Returns:
            _type_: Returns the file path of the written JSON file if successful, otherwise None.
        """
        file_path: str = os.path.join(
            self.current_date_import_folder, f"{filename}.json"
        )
        try:
            with open(file_path, "w") as file:
                json.dump(data, file, indent=4)
            log_info(msg=f"Successfully wrote {filename} to {file_path}")
            return f"{file_path}"
        except Exception as e:
            log_error(msg=f"Error writing {filename} to {file_path}: {e}")
        return


if __name__ == "__main__":
    from src.services.importers.mf.incremental.csv_converter_service import (
        CSVConverterService,
    )

    def date_range(start_date: date, end_date: date):
        """Generates a range of dates from start_date to end_date."""
        delta: int = (end_date - start_date).days + 1
        for i in range(delta):
            yield start_date + timedelta(i)

    async def main():
        start_date: date = date(2025, 6, 3)
        end_date: date = date(2025, 6, 3)
        service = IncrementalImporterService()
        for current_date in date_range(start_date=start_date, end_date=end_date):
            date_str: str = current_date.strftime("%d-%m-%Y")
            await service.invoke(current_date=date_str)
            current_date_import_folder: str = service.current_date_import_folder
            csv_converter_service: CSVConverterService = CSVConverterService(
                json_folder=current_date_import_folder
            )
            await csv_converter_service.invoke()
            log_info(
                msg=f"CSV conversion completed for date: {date_str}, folder: {current_date_import_folder}"
            )

    asyncio.run(main())
