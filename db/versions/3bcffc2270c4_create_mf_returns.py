"""create_mf_returns

Revision ID: 3bcffc2270c4
Revises: e1c3f4048cba
Create Date: 2025-04-07 15:12:15.465004

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "3bcffc2270c4"
down_revision: Union[str, None] = "e1c3f4048cba"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "mf_return",
        sa.Column("schemecode", sa.Numeric(18, 6), nullable=False),
        sa.Column("c_date", sa.DateTime(), nullable=True),
        sa.Column("p_date", sa.DateTime(), nullable=True),
        sa.Column("c_nav", sa.Numeric(18, 6), nullable=True),
        sa.Column("p_nav", sa.Numeric(18, 6), nullable=True),
        sa.Column("1dayret", sa.Numeric(18, 6), nullable=True),
        sa.Column("1weekdate", sa.DateTime(), nullable=True),
        sa.Column("1weeknav", sa.Numeric(18, 6), nullable=True),
        sa.Column("1weekret", sa.Numeric(18, 6), nullable=True),
        sa.Column("1mthdate", sa.DateTime(), nullable=True),
        sa.Column("1mthnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("1monthret", sa.Numeric(18, 6), nullable=True),
        sa.Column("3mthdate", sa.DateTime(), nullable=True),
        sa.Column("3mthnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("3monthret", sa.Numeric(18, 6), nullable=True),
        sa.Column("6mntdate", sa.DateTime(), nullable=True),
        sa.Column("6mnthnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("6monthret", sa.Numeric(18, 6), nullable=True),
        sa.Column("9mnthdate", sa.DateTime(), nullable=True),
        sa.Column("9mnthnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("9mnthret", sa.Numeric(18, 6), nullable=True),
        sa.Column("1yrdate", sa.DateTime(), nullable=True),
        sa.Column("1yrnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("1yrret", sa.Numeric(18, 6), nullable=True),
        sa.Column("2yrdate", sa.DateTime(), nullable=True),
        sa.Column("2yrnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("2yearret", sa.Numeric(18, 6), nullable=True),
        sa.Column("3yrdate", sa.DateTime(), nullable=True),
        sa.Column("3yrnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("3yearret", sa.Numeric(18, 6), nullable=True),
        sa.Column("4yearret", sa.Numeric(18, 6), nullable=True),
        sa.Column("4yrdate", sa.DateTime(), nullable=True),
        sa.Column("4yrnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("5yrdate", sa.DateTime(), nullable=True),
        sa.Column("5yrnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("5yearret", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.Column("incdate", sa.DateTime(), nullable=True),
        sa.Column("incnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("incret", sa.Numeric(18, 6), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="mf_return_schemecode_idx",
        table_name="mf_return",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("mf_return")
