[project]
name = "kairo"
version = "0.1.0"
description = ""
authors = [
    {name = "ragequilt",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic (>=1.15.1,<2.0.0)",
    "sqlalchemy (>=2.0.38,<3.0.0)",
    "asyncpg (>=0.30.0,<0.31.0)",
    "fastapi (>=0.115.11,<0.116.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "psycopg2 (>=2.9.10,<3.0.0)",
    "greenlet (>=3.1.1,<4.0.0)",
    "python-dateutil (>=2.9.0.post0,<3.0.0)",
    "pytz (>=2025.2,<2026.0)",
    "python-json-logger (>=3.3.0,<4.0.0)",
    "ipython (>=9.1.0,<10.0.0)",
    "pytest (>=8.3.5,<9.0.0)",
    "pytest-asyncio (>=0.26.0,<0.27.0)",
    "pandas (>=2.2.3,<3.0.0)",
    "aiohttp (>=3.12.11,<4.0.0)",
    "nanoid (>=2.0.0,<3.0.0)",
    "celery (>=5.5.3,<6.0.0)",
    "celery-redbeat (>=2.3.2,<3.0.0)",
    "certifi (>=2025.6.15,<2026.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
