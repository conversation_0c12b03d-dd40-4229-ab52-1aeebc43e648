"""create_mf_rations_defaultbm

Revision ID: bb4c8acb57cd
Revises: e3f1a3ba2a18
Create Date: 2025-04-27 11:33:41.204517

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "bb4c8acb57cd"
down_revision: Union[str, None] = "e3f1a3ba2a18"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the mf_ratios_defaultbm table based on the provided schema.
    This table contains scheme ratio default benchmark information (1 Year ratios calculated daily).
    """
    op.create_table(
        "mf_ratios_defaultbm",  # Table name in lowercase as requested
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Scheme Code",
        ),  # Using BigInteger as requested for Integer from source [14]
        sa.Column(
            "upddate", sa.DateTime, nullable=True, comment="Updation Date"
        ),  # Datetime from source [14]
        sa.Column(
            "datefrom",
            sa.DateTime,
            nullable=True,
            comment="Ratio calculation ‘from’ date range",
        ),  # Datetime from source [14]
        sa.Column(
            "dateto",
            sa.DateTime,
            nullable=True,
            comment="Ratio calculation ‘to’ date range",
        ),  # Datetime from source [14]
        sa.Column(
            "average",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Average Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [14], lowercase
        sa.Column(
            "bmaverage",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Average Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [14], lowercase
        sa.Column(
            "sd",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Standard Deviation of the scheme",
        ),  # Using Numeric(18,6) for Float from source [14], lowercase
        sa.Column(
            "bmsd",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Standard Deviation of the benchmark",
        ),  # Using Numeric(18,6) for Float from source [14], lowercase
        sa.Column(
            "semisd",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Semi Standard Deviation of the scheme",
        ),  # Using Numeric(18,6) for Float from source [14], lowercase
        sa.Column(
            "semisdii",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Semi Standard Deviation of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "beta", sa.Numeric(18, 6), nullable=True, comment="Beta of the scheme"
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "correlation",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Corelation of the scheme",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "beta_corelation",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Beta Corelation of the scheme",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "covariance",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Covariance of the scheme",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "treynor",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Treynor Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "fama", sa.Numeric(18, 6), nullable=True, comment="FAMA Ratio of the scheme"
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "sharpe",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Sharpe Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "sharpe_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Sharpe Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "sharpe_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Sharpe Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "jalpha_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Jenson’s Alpha of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "jalpha_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Jenson’s Alpha of the scheme",
        ),  # Using Numeric(18,6) for Float from source [15], lowercase
        sa.Column(
            "sortino_x",
            sa.Numeric(18, 6),
            nullable=True,
            comment="SORTINO Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [16], lowercase
        sa.Column(
            "sortino_y",
            sa.Numeric(18, 6),
            nullable=True,
            comment="SORTINO Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [16], lowercase
        sa.Column(
            "flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"
        ),  # Varchar(1) from source [16], lowercase
        sa.Column(
            "alpha", sa.Numeric(18, 6), nullable=True, comment="Alpha of the scheme"
        ),  # Using Numeric(18,6) for Float from source [16], lowercase
        sa.Column(
            "sortino",
            sa.Numeric(18, 6),
            nullable=True,
            comment="SORTINO Ratio of the scheme",
        ),  # Using Numeric(18,6) for Float from source [16], lowercase
        sa.Column(
            "sortinoii",
            sa.Numeric(18, 6),
            nullable=True,
            comment="SORTINO Ratio of the benchmark index",
        ),  # Using Numeric(18,6) for Float from source [16], lowercase
        sa.Column(
            "ret_improper",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return due to Improper",
        ),  # Using Numeric(18,6) for Float from source [16], lowercase
        sa.Column(
            "ret_selectivity",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return due to Selectivity",
        ),  # Using Numeric(18,6) for Float from source [17], lowercase
        sa.Column(
            "down_probability",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Downside Probability",
        ),  # Using Numeric(18,6) for Float from source [17], lowercase
        sa.Column(
            "rsquared", sa.Numeric(18, 6), nullable=True, comment="R-Squared"
        ),  # Using Numeric(18,6) for Float from source [17], lowercase
        sa.Column(
            "trackingerror", sa.Numeric(18, 6), nullable=True, comment="Tracking Error"
        ),  # Using Numeric(18,6) for Float from source [17], lowercase and snake_case
        sa.Column(
            "down_risk",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Down Side Risk - Ignore this field",
        ),  # Using Numeric(18,6) for Float from source [17], lowercase, added ignore comment
        sa.Column(
            "sd_annualised",
            sa.Numeric(18, 6),
            nullable=True,
            comment="SD Annualised -  Ignore this field",
        ),  # Using Numeric(18,6) for Float from source [17], lowercase, added ignore comment
        sa.Column(
            "informationratio",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Information Ratio",
        ),  # Using Numeric(18,6) for Float from source [17], lowercase and snake_case
        # Primary key is 'schemecode' based on the source [14]
        sa.PrimaryKeyConstraint("schemecode"),
    )


def downgrade():
    """
    Drops the mf_ratios_defaultbm table.
    """
    op.drop_table("mf_ratios_defaultbm")
