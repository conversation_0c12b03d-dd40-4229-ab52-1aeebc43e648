"""scheme_index_part

Revision ID: e6c3abc4b539
Revises: 7544249818c5
Create Date: 2025-03-09 17:14:32.632216

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e6c3abc4b539"
down_revision: Union[str, None] = "7544249818c5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_index_part",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column("indexcode", sa.BigInteger(), nullable=False),
        sa.Column("benchmark_weightage", sa.Numeric(18, 6), nullable=True),
        sa.Column("indexorder", sa.Integer(), nullable=True),
        sa.Column("remark", sa.String(length=100), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "indexcode"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_index_part_schemecode_idx",
        table_name="scheme_index_part",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="scheme_index_part_indexcode_idx",
        table_name="scheme_index_part",
        columns=["indexcode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_index_part")
