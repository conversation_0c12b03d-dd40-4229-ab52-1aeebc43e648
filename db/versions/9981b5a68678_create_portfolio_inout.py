"""create_portfolio_inout

Revision ID: 9981b5a68678
Revises: 16c7b44eaea6
Create Date: 2025-03-09 17:46:21.555387

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "9981b5a68678"
down_revision: Union[str, None] = "16c7b44eaea6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "portfolio_inout",
        sa.Column("fincode", sa.BigInteger(), nullable=False),
        sa.Column("invdate", sa.DateTime(), nullable=False),
        sa.Column("mode", sa.String(length=5), nullable=False),
        sa.Column("compname", sa.String(length=255), nullable=True),
        sa.Column("s_name", sa.String(length=150), nullable=True),
        sa.Column("mktval", sa.Numeric(18, 6), nullable=True),
        sa.Column("noshares", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("fincode", "invdate", "mode"),
    )

    op.execute("COMMIT;")
    op.create_index(
        index_name="portfolio_inout_fincode_idx",
        table_name="portfolio_inout",
        columns=["fincode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("portfolio_inout")
