"""create_mfbulkdeals

Revision ID: 0bc996a7fece
Revises: bb4c8acb57cd
Create Date: 2025-04-27 11:38:58.927596

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "0bc996a7fece"
down_revision: Union[str, None] = "bb4c8acb57cd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # Create mfbulkdeals table based on Source [1-3]
    op.create_table(
        "mfbulkdeals",
        sa.Column(
            "fincode", sa.BigInteger(), nullable=False
        ),  # Integer -> BigInteger [3]
        sa.Column("date", sa.DateTime(), nullable=False),  # Datetime [3]
        sa.Column("exchange", sa.String(length=50), nullable=True),  # Varchar(50) [3]
        sa.Column(
            "clientname", sa.String(length=255), nullable=False
        ),  # Varchar(255) [3]
        sa.Column("type", sa.String(length=50), nullable=True),  # Varchar(50) [3]
        sa.Column(
            "mfcode", sa.BigInteger(), nullable=True
        ),  # Integer -> BigInteger [3]
        sa.Column("dealtype", sa.String(length=5), nullable=False),  # NVarchar(5) [3]
        sa.Column(
            "volume", sa.Numeric(precision=18, scale=6), nullable=False
        ),  # Numeric(18,0) -> Numeric(18,6) [3]
        sa.Column(
            "price", sa.Numeric(precision=18, scale=6), nullable=False
        ),  # Float -> Numeric(18,6) [3]
        sa.Column("flag", sa.String(length=1), nullable=True),  # Varchar(1) [3]
        # Composite Primary Key based on Source [3]
        sa.PrimaryKeyConstraint(
            "fincode", "date", "clientname", "dealtype", "volume", "price"
        ),
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("mfbulkdeals")
