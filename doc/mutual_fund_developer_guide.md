# Mutual Fund Database Developer Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Mutual Fund Domain Concepts](#mutual-fund-domain-concepts)
3. [Database Architecture Overview](#database-architecture-overview)
4. [Core Entity Groups](#core-entity-groups)
5. [Key Business Workflows](#key-business-workflows)
6. [Data Relationships](#data-relationships)
7. [Technical Implementation Notes](#technical-implementation-notes)
8. [Glossary](#glossary)

## Introduction

This guide provides comprehensive documentation for developers working with the Kairo mutual fund database system. It assumes basic programming and database knowledge but minimal understanding of the mutual fund industry.

The database contains 50+ tables that model the complete mutual fund ecosystem, from fund companies and their products to investor transactions and performance analytics.

## Mutual Fund Domain Concepts

### What is a Mutual Fund?

A mutual fund is an investment vehicle that pools money from many investors to purchase securities like stocks, bonds, or other assets. Think of it as a shared investment account managed by professionals.

### Key Players

1. **AMC (Asset Management Company)**: The company that creates and manages mutual funds
   - Examples: HDFC Mutual Fund, ICICI Prudential, SBI Mutual Fund
   - Each AMC can offer multiple fund schemes

2. **Scheme**: A specific mutual fund product offered by an AMC
   - Example: "HDFC Equity Fund" or "SBI Blue Chip Fund"
   - Each scheme has specific investment objectives and strategies

3. **Fund Manager**: The professional who makes investment decisions for a scheme
   - Can manage multiple schemes
   - Their experience and track record are important factors

4. **Registrar & Transfer Agent (RTA)**: Companies that handle investor records and transactions
   - Examples: CAMS, Karvy, etc.

### Investment Concepts

#### NAV (Net Asset Value)
- The per-unit price of a mutual fund scheme
- Calculated daily based on the market value of underlying assets
- Formula: NAV = (Total Assets - Total Liabilities) / Total Units Outstanding

#### Investment Options
1. **SIP (Systematic Investment Plan)**: Regular monthly investments
2. **SWP (Systematic Withdrawal Plan)**: Regular monthly withdrawals
3. **STP (Systematic Transfer Plan)**: Regular transfers between schemes

#### Fund Categories
- **Equity Funds**: Invest primarily in stocks
- **Debt Funds**: Invest in bonds and fixed-income securities
- **Hybrid Funds**: Mix of equity and debt
- **ETF (Exchange Traded Fund)**: Traded on stock exchanges

## Database Architecture Overview

The database is organized into several logical groups:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AMC & Fund    │    │   Performance   │    │   Transactions  │
│   Management    │    │   & Analytics   │    │   & Holdings    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Master Data &  │
                    │  Classifications│
                    └─────────────────┘
```

## Core Entity Groups

### 1. AMC and Fund Management

**Primary Tables:**
- `amc_mst_new`: Asset Management Companies
- `scheme_master`: Basic scheme information
- `scheme_details`: Detailed scheme configuration
- `fundmanager_mst`: Fund manager profiles
- `dailyfundmanager`: Daily fund manager assignments

**Key Relationships:**
```
AMC (1) ──→ (Many) Schemes ──→ (Many) Fund Managers
```

### 2. Master Data and Classifications

**Purpose**: Standardize categorization across the system

**Tables:**
- `type_mst`: Fund types (Equity, Debt, Hybrid, etc.)
- `option_mst`: Investment options (Growth, Dividend, etc.)
- `sclass_mst`: Asset classes and categories
- `plan_mst`: Plan types (Regular, Direct)
- `rt_mst`: Registrar and Transfer Agents

### 3. NAV and Performance Data

**Tables:**
- `currentnav`: Latest NAV for each scheme
- `navhist`: Historical NAV data
- `mf_return`: Return calculations (1 day, 1 week, 1 month, etc.)
- `mf_abs_return`: Absolute returns
- `mf_ans_return`: Annualized returns
- `mf_cagr_return`: CAGR (Compound Annual Growth Rate) returns

**Performance Metrics:**
- Daily, weekly, monthly, yearly returns
- Since inception returns
- Risk-adjusted ratios (Sharpe, Treynor, etc.)

### 4. Investment Transactions

**SIP (Systematic Investment Plan):**
- Table: `mf_sip`
- Allows investors to invest fixed amounts regularly
- Configurable frequency (monthly, quarterly, etc.)

**SWP (Systematic Withdrawal Plan):**
- Table: `mf_swp`
- Allows regular withdrawals from investments
- Useful for generating regular income

**STP (Systematic Transfer Plan):**
- Table: `mf_stp`
- Transfers between different schemes
- Helps in portfolio rebalancing

### 5. Portfolio Holdings

**Tables:**
- `mf_portfolio`: What securities each scheme holds
- `companymaster`: Company information for equity holdings
- `industry_mst`: Industry classifications
- `asect_mst`: Asset sector classifications

**Key Metrics:**
- Number of shares held
- Market value of holdings
- Percentage of total portfolio

### 6. AUM (Assets Under Management)

**Tables:**
- `amc_aum`: Total AUM for each AMC
- `scheme_aum`: AUM for individual schemes
- `avg_scheme_aum`: Average AUM calculations

**Business Importance:**
- Indicates fund size and investor confidence
- Affects expense ratios and fund viability

### 7. Risk and Analytics

**Tables:**
- `mf_ratio`: Risk ratios comparing scheme vs benchmark
- `mf_ratios_defaultbm`: Default benchmark comparisons
- `avg_maturity`: For debt funds - average maturity of holdings
- `expenceratio`: Annual expense ratios

**Key Metrics:**
- Beta: Volatility relative to market
- Alpha: Excess returns over benchmark
- Sharpe Ratio: Risk-adjusted returns
- Standard Deviation: Volatility measure

## Key Business Workflows

### 1. Daily NAV Calculation
```
Market Close → Portfolio Valuation → NAV Calculation → NAV Publication
```

### 2. Performance Analysis
```
Historical NAV → Return Calculations → Risk Metrics → Benchmark Comparison
```

### 3. Portfolio Management
```
Investment Decisions → Buy/Sell Securities → Update Holdings → Impact on NAV
```

### 4. Investor Transactions
```
Investment Request → Unit Allocation → Portfolio Update → Confirmation
```

## Data Relationships

### Core Hierarchy
```
AMC
├── Schemes
│   ├── NAV History
│   ├── Performance Returns
│   ├── Portfolio Holdings
│   ├── SIP/SWP/STP Options
│   ├── Expense Ratios
│   └── Risk Metrics
└── Fund Managers
```

### Master Data Connections
```
Scheme Details
├── Type (Equity/Debt/Hybrid)
├── Option (Growth/Dividend)
├── Asset Class (Large Cap/Mid Cap/etc.)
├── Plan (Regular/Direct)
└── Registrar (CAMS/Karvy/etc.)
```

## Technical Implementation Notes

### Data Types
- **Numeric Fields**: Use `numeric(18,6)` for financial precision
- **Codes**: Integer types for efficient joins and indexing
- **Flags**: Single character fields for status indicators
- **Dates**: Timestamp fields for temporal data

### Performance Considerations
- Heavy indexing on scheme codes and dates
- Partitioning recommended for large historical tables
- Consider read replicas for analytics workloads

### Data Quality
- All tables include `flag` fields for soft deletes
- Extensive use of foreign key relationships
- Regular data validation processes needed

### Common Patterns
1. **Composite Primary Keys**: Many tables use (schemecode, date) combinations
2. **Audit Fields**: Most tables include update flags and timestamps
3. **Hierarchical Data**: Master-detail relationships throughout

## Glossary

**AUM**: Assets Under Management - Total value of assets managed by a fund

**AMFI**: Association of Mutual Funds in India - Industry body that assigns codes

**CAGR**: Compound Annual Growth Rate - Annualized return calculation

**CAMS**: Computer Age Management Services - Major registrar company

**ETF**: Exchange Traded Fund - Funds traded on stock exchanges

**ISIN**: International Securities Identification Number - Unique security identifier

**NAV**: Net Asset Value - Per unit price of a mutual fund

**NFO**: New Fund Offer - Initial offering period for new schemes

**RTA**: Registrar and Transfer Agent - Handles investor records

**SEBI**: Securities and Exchange Board of India - Market regulator

**SIP/SWP/STP**: Systematic Investment/Withdrawal/Transfer Plans

## Detailed Table Descriptions

### Core Tables Deep Dive

#### amc_mst_new (Asset Management Companies)
```sql
Key Fields:
- amc_code (PK): Unique identifier for each AMC
- amc: Company name (e.g., "HDFC Asset Management Company Limited")
- mf_type: Type of mutual fund company
- trustee_name: Name of the trustee company
- sponsor_name: Sponsoring organization
- amc_inc_date: Date of incorporation
```

**Business Context**: AMCs are the heart of the mutual fund industry. Each AMC can launch multiple schemes and must be registered with SEBI.

#### scheme_details (Comprehensive Scheme Information)
```sql
Key Fields:
- schemecode (PK): Unique scheme identifier
- amc_code (FK): Links to AMC
- amfi_code: AMFI assigned code
- isin_code: International identifier
- type_code: Fund type (equity/debt/hybrid)
- classcode: Asset class classification
- nfo_open/nfo_close: New Fund Offer dates
- incept_date: Scheme launch date
- incept_nav: Starting NAV (usually ₹10)
- sip/swp/stp: Available transaction types
```

**Business Context**: This is the master table for all scheme information. Every scheme must have an entry here before any transactions can occur.

#### currentnav (Latest NAV Data)
```sql
Key Fields:
- schemecode (PK): Links to scheme
- navdate: Date of NAV calculation
- navrs: Current NAV value
- repurprice: Repurchase price (for selling)
- saleprice: Sale price (for buying)
- change: Day-over-day change
- prevnav: Previous day's NAV
```

**Business Context**: NAV is calculated daily after market close. This table always contains the most recent NAV for each active scheme.

#### mf_portfolio (Scheme Holdings)
```sql
Key Fields:
- schemecode, invdate, srno (Composite PK)
- fincode: Company identifier
- noshares: Number of shares held
- mktval: Market value of holding
- holdpercentage: % of total portfolio
- compname: Company name
- rating: Credit rating (for debt securities)
```

**Business Context**: Shows what securities each scheme holds. Updated monthly or quarterly based on disclosure requirements.

### Transaction Tables

#### mf_sip (Systematic Investment Plans)
```sql
Key Fields:
- schemecode, frequency (Composite PK)
- sipmininvest: Minimum SIP amount
- sipfrequencyno: Frequency number (1=monthly, 3=quarterly)
- sipminimumperiod: Minimum commitment period
```

**Business Context**: SIPs are popular in India for disciplined investing. Allows investors to invest small amounts regularly.

#### mf_swp (Systematic Withdrawal Plans)
```sql
Similar structure to SIP but for withdrawals
Used by retirees for regular income
```

#### mf_stp (Systematic Transfer Plans)
```sql
Additional field: stpinout (I=In, O=Out)
Allows automatic rebalancing between schemes
```

### Performance and Analytics Tables

#### mf_return (Return Calculations)
```sql
Key Fields:
- Various period returns: 1dayret, 1weekret, 1monthret, etc.
- Corresponding NAV values and dates
- incret: Since inception return
```

**Business Context**: Pre-calculated returns for different time periods. Critical for performance comparison and investor decision-making.

#### mf_ratio (Risk Metrics)
```sql
Key Fields:
- beta_x/beta_y: Benchmark vs scheme beta
- sharpe_x/sharpe_y: Risk-adjusted returns
- treynor_x/treynor_y: Return per unit of systematic risk
- sd_x/sd_y: Standard deviation (volatility)
```

**Business Context**: Risk metrics help investors understand volatility and risk-adjusted performance compared to benchmarks.

## Data Flow Patterns

### Daily Operations Flow
```
1. Market Data → Portfolio Valuation
2. Portfolio Valuation → NAV Calculation
3. NAV Calculation → currentnav table
4. currentnav → navhist (historical record)
5. navhist → Return Calculations
6. Returns → Risk Ratio Updates
```

### Monthly/Quarterly Operations
```
1. Portfolio Disclosure → mf_portfolio updates
2. AUM Calculation → scheme_aum updates
3. Expense Calculation → expenceratio updates
4. Performance Analysis → Ratio recalculations
```

### Investor Transaction Flow
```
1. Transaction Request → Validation against scheme_details
2. Unit Calculation → Based on currentnav
3. Portfolio Impact → Update scheme_aum
4. Confirmation → Transaction record creation
```

## Common Query Patterns

### Get Scheme Performance
```sql
SELECT
    sd.scheme_name,
    cn.navrs as current_nav,
    mr.1monthret,
    mr.1yrret,
    mr.incret
FROM scheme_details sd
JOIN currentnav cn ON sd.schemecode = cn.schemecode
JOIN mf_return mr ON sd.schemecode = mr.schemecode
WHERE sd.amc_code = ?
```

### Portfolio Analysis
```sql
SELECT
    p.compname,
    p.holdpercentage,
    p.mktval,
    cm.industry
FROM mf_portfolio p
JOIN companymaster cm ON p.fincode = cm.fincode
WHERE p.schemecode = ?
AND p.invdate = (SELECT MAX(invdate) FROM mf_portfolio WHERE schemecode = ?)
ORDER BY p.holdpercentage DESC
```

### SIP Options for Scheme
```sql
SELECT
    frequency,
    sipmininvest,
    sipfrequencyno
FROM mf_sip
WHERE schemecode = ?
AND flag = 'Y'
ORDER BY sipfrequencyno
```

---

*This documentation provides a comprehensive foundation for understanding the mutual fund database structure. For implementation details and specific business rules, consult with domain experts and refer to regulatory guidelines.*
