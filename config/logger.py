from functools import partial
from typing import Dict, Any
from src.middleware.request_ctx import _get_request_id, _get_trace_id
from config.cfg import cfg, is_dev, is_test
import platform
import logging
from logging import Logger
import sys
import socket
import traceback
from logging.handlers import <PERSON>ysLogHandler
from pythonjsonlogger.json import JsonF<PERSON>atter

# from logtail import LogtailHandler, LogtailContext
# import sentry_sdk


async def async_capture_exc(error: Exception) -> None:
    if is_dev() or is_test():
        return None

    # sentry_sdk.capture_exception(error=error)
    return None


def capture_exc(error: Exception) -> None:
    if is_dev() or is_test():
        return None

    # sentry_sdk.capture_exception(error=error)
    return None


def get_logger():
    return log


def set_log_level(level: str):
    log.setLevel(level)
    return log


def log_debug(msg: str, loc: str = None, method: str = None):
    if cfg("ENV") != "prod":
        log.debug(msg=_build_log_msg(msg=msg, loc=loc, method=method))


def log_info(msg: str, loc: str = None, method: str = None):
    log.info(msg=_build_log_msg(msg=msg, loc=loc, method=method))


def log_error(
    msg: str,
    loc: str = None,
    method: str = None,
    e: Exception = None,
    stack_trace: bool = True,
):
    if not stack_trace:
        log.error(
            msg=_build_log_msg(msg=msg, loc=loc, method=method),
        )
    else:
        log.error(
            msg=_build_log_msg(msg=msg, loc=loc, method=method),
            exc_info=True,
            stack_info=True,
            stacklevel=1,
        )

    if e and (not is_test() or not is_dev()):
        capture_exc(error=e)
    return


def _build_log_msg(msg: str, loc: str = None, method: str = None) -> Dict:
    log_msg: Dict[str, Any] = {}
    log_msg["message"] = msg
    if loc:
        log_msg["file"] = loc

    if method:
        log_msg["method"] = method

    return log_msg


class ContextFilter(logging.Filter):
    hostname = socket.gethostname()

    def filter(self, record=None):
        if record:
            record.hostname = ContextFilter.hostname
        return True


# Add filter to the logger
class EndpointFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        if "GET /up" in record.msg:
            return False
        else:
            return True


def add_hostname_updated_recordfactory(
    default_factory: logging.LogRecord, *args, **kwargs
):
    record = default_factory(*args, **kwargs)  # type: ignore
    record.hostname = platform.node() or socket.gethostname()
    return record


# logging.basicConfig()
default_factory = logging.getLogRecordFactory()
hostname_logrecord_factory = partial(
    add_hostname_updated_recordfactory, default_factory
)
logging.setLogRecordFactory(hostname_logrecord_factory)
logging.getLogger("botocore").setLevel(logging.CRITICAL)
logging.getLogger("s3fs").setLevel(logging.CRITICAL)
logging.getLogger("urllib3").setLevel(logging.CRITICAL)
logging.getLogger("fsspec").setLevel(logging.CRITICAL)
logging.getLogger("celery").setLevel(logging.INFO)
logging.getLogger("pypdf").setLevel(logging.ERROR)
logging.getLogger("pydantic").setLevel(logging.ERROR)
stream_handler: logging.StreamHandler = logging.StreamHandler(sys.stdout)
json_formatter: JsonFormatter = JsonFormatter(
    defaults={
        "environment": cfg("ENV"),
        "application": "kairo",
        "request_id": _get_trace_id(),
        "trace_id": _get_request_id(),
    },
)
log: Logger = logging.getLogger("kairo")
log.addHandler(stream_handler)
log.setLevel(logging.DEBUG)
stream_handler.setFormatter(json_formatter)


if not is_dev() and not is_test():
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    stream_handler.setFormatter(formatter)
    log.addHandler(stream_handler)
    log_info(msg=f"Initializing logger for {cfg('ENV')}")

else:
    log_info(msg=f'Not initialising logger for {cfg("ENV")}', loc=f"{__name__}")
