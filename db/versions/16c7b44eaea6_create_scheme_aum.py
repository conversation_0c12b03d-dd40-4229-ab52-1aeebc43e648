"""create_scheme_aum

Revision ID: 16c7b44eaea6
Revises: 9b100bbd7b48
Create Date: 2025-03-09 17:44:41.912025

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "16c7b44eaea6"
down_revision: Union[str, None] = "9b100bbd7b48"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_aum",
        sa.Column("schemecode", sa.BigInteger(), nullable=False),
        sa.Column("date", sa.DateTime(), nullable=False),
        sa.Column("exfof", sa.Float(), nullable=True),
        sa.Column("fof", sa.Float(), nullable=True),
        sa.Column("total", sa.Float(), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "date"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_aum_schemecode_idx",
        table_name="scheme_aum",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_aum")
