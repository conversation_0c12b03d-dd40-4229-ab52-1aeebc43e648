erDiagram
    %% Mutual Fund Database Entity Relationship Diagram
    %% This diagram shows the core entities and relationships in the Kairo mutual fund database
    
    %% Core AMC and Scheme Entities
    amc_mst_new {
        int8 amc_code PK
        varchar amc
        varchar fund
        varchar mf_type
        varchar trustee_name
        text sponsor_name
        timestamp amc_inc_date
        varchar amc_symbol
        varchar city
        varchar flag
    }
    
    scheme_master {
        int8 schemecode PK
        int8 amc_code FK
        varchar scheme_name
        varchar color
        varchar flag
    }
    
    scheme_details {
        int8 schemecode PK
        int8 amfi_code
        varchar cams_code
        int8 amc_code FK
        varchar s_name
        varchar amfi_name
        varchar isin_code
        int4 type_code FK
        int4 opt_code FK
        int4 classcode FK
        int4 rt_code FK
        int4 plan FK
        numeric init_price
        numeric offerprice
        timestamp nfo_open
        timestamp nfo_close
        timestamp incept_date
        numeric incept_nav
        varchar sip
        varchar swp
        varchar stp
        varchar flag
    }
    
    %% Master Data Tables
    type_mst {
        int8 type_code PK
        varchar type
        varchar flag
    }
    
    option_mst {
        int8 opt_code PK
        varchar option
        varchar flag
    }
    
    sclass_mst {
        int8 classcode PK
        varchar classname
        int8 asset_code
        varchar asset_type
        varchar category
        varchar sub_category
        varchar flag
    }
    
    rt_mst {
        int8 rt_code PK
        varchar rt_name
        varchar sebi_reg_no
        varchar address1
        varchar website
        varchar email
        varchar flag
    }
    
    plan_mst {
        int8 plan_code PK
        varchar plan
        varchar flag
    }
    
    %% Fund Manager Tables
    fundmanager_mst {
        int8 id PK
        varchar initial
        varchar fundmanager
        varchar qualification
        varchar experience
        varchar designation
        int4 age
        timestamp reporteddate
        varchar flag
    }
    
    dailyfundmanager {
        timestamp date PK
        int8 amc PK
        int8 schemecode PK
        int8 fundmanger1 FK
        int8 fundmanger2 FK
        int8 fundmanger3 FK
        int8 fundmanger4 FK
        varchar flag
    }
    
    %% NAV and Performance Tables
    currentnav {
        int8 schemecode PK
        timestamp navdate
        numeric navrs
        numeric repurprice
        numeric saleprice
        timestamp cldate
        numeric change
        numeric netchange
        numeric prevnav
        timestamp prenavdate
        varchar flag
    }
    
    navhist {
        int8 schemecode PK
        timestamp navdate PK
        numeric navrs
        numeric repurprice
        numeric saleprice
        numeric adjustednav_c
        numeric adjustednav_nonc
        varchar flag
    }
    
    mf_return {
        numeric schemecode PK
        timestamp c_date
        timestamp p_date
        numeric c_nav
        numeric p_nav
        numeric 1dayret
        numeric 1weekret
        numeric 1monthret
        numeric 3monthret
        numeric 6monthret
        numeric 1yrret
        numeric 2yearret
        numeric 3yearret
        numeric 5yearret
        numeric incret
        varchar flag
    }
    
    %% Transaction Tables (SIP, SWP, STP)
    mf_sip {
        int8 schemecode PK
        int8 amc_code FK
        varchar frequency PK
        varchar sip
        varchar sipdatescondition
        numeric sipmininvest
        numeric sipaddninvest
        int4 sipfrequencyno
        int4 sipminimumperiod
        varchar sipmaximumperiod
        varchar flag
    }
    
    mf_swp {
        int8 schemecode PK
        int8 amc_code FK
        varchar frequency PK
        varchar swp
        varchar swpdatescondition
        numeric swpmininvest
        numeric swpaddninvest
        int4 swpfrequencyno
        int4 swpminimumperiod
        varchar flag
    }
    
    mf_stp {
        int8 schemecode PK
        int8 amc_code FK
        varchar frequency PK
        varchar stpinout PK
        varchar stp
        varchar stpdatescondition
        numeric stpmininvest
        numeric stpaddninvest
        int4 stpfrequencyno
        int4 stpminimumperiod
        varchar flag
    }
    
    %% Portfolio and Holdings
    mf_portfolio {
        int8 schemecode PK
        timestamp invdate PK
        int8 srno PK
        int8 fincode FK
        int8 asect_code FK
        int8 sect_code
        numeric noshares
        numeric mktval
        numeric aum
        numeric holdpercentage
        varchar compname
        varchar sect_name
        varchar asect_name
        varchar rating
        varchar flag
    }
    
    companymaster {
        int8 fincode PK
        int8 scripcode
        varchar symbol
        varchar compname
        varchar s_name
        int4 ind_code FK
        varchar industry
        varchar isin
        varchar status
        varchar series
        float8 fv
        varchar flag
    }
    
    industry_mst {
        int8 ind_code PK
        varchar industry
        varchar ind_shortname
        varchar sector
        int4 sector_code
        varchar flag
    }
    
    asect_mst {
        int4 asect_code PK
        varchar asect_type
        varchar asset
        varchar as_name
        varchar flag
    }
    
    %% AUM Tables
    amc_aum {
        int8 amc_code PK
        timestamp aumdate PK
        numeric totalaum
        varchar flag
    }
    
    scheme_aum {
        int8 schemecode PK
        timestamp date PK
        float8 exfof
        float8 fof
        float8 total
        varchar flag
    }
    
    %% Benchmark and Index Tables
    index_mst {
        int8 indexcode PK
        int8 fincode FK
        int8 scripcode
        varchar indexname
        varchar index_gp
        varchar subgroup
        varchar flag
    }
    
    scheme_index_part {
        int8 schemecode PK
        int8 indexcode PK
        numeric benchmark_weightage
        int4 indexorder
        varchar remark
        varchar flag
    }
    
    bm_annualisedreturn {
        int8 index_code PK
        varchar symbol
        int8 scripcode
        timestamp date
        numeric close
        numeric 1dayret
        numeric 1weekret
        numeric 1monthret
        numeric 3monthret
        numeric 6monthret
        numeric 1yrret
        numeric 2yearret
        numeric 3yearret
        numeric 5yearret
        numeric incret
        varchar flag
    }
    
    %% Dividend and Load Tables
    div_mst {
        int4 div_code PK
        varchar div_type
        varchar flag
    }
    
    divdetails {
        int8 schemecode PK
        timestamp recorddate PK
        int8 amc_code FK
        int8 div_code FK
        timestamp exdivdate
        numeric bonusrate1
        numeric bonusrate2
        numeric gross
        numeric corporate
        numeric noncorporate
        varchar status
        varchar flag
    }
    
    schemeload {
        int8 schemecode PK
        timestamp ldate PK
        int8 ltypecode PK
        int8 lsrno PK
        numeric frmamount
        numeric uptoamount
        int4 minperiod
        int4 maxperiod
        numeric entryload
        numeric exitload
        text remarks
        varchar flag
    }
    
    loadtype_mst {
        int8 ltypecode PK
        varchar load
        varchar flag
    }
    
    %% Risk and Ratio Tables
    mf_ratio {
        int8 schemecode PK
        timestamp upddate
        timestamp datefrom
        timestamp dateto
        numeric avg_x
        numeric avg_y
        numeric sd_x
        numeric sd_y
        numeric beta_x
        numeric beta_y
        numeric sharpe_x
        numeric sharpe_y
        numeric treynor_x
        numeric treynor_y
        varchar flag
    }
    
    expenceratio {
        int8 schemecode PK
        timestamp date PK
        int8 amc_code FK
        numeric expratio
        varchar flag
    }

    %% Relationships
    amc_mst_new ||--o{ scheme_master : "manages"
    amc_mst_new ||--o{ scheme_details : "offers"
    amc_mst_new ||--o{ mf_sip : "provides"
    amc_mst_new ||--o{ mf_swp : "provides"
    amc_mst_new ||--o{ mf_stp : "provides"
    amc_mst_new ||--o{ amc_aum : "has"
    amc_mst_new ||--o{ divdetails : "declares"
    amc_mst_new ||--o{ expenceratio : "reports"
    amc_mst_new ||--o{ dailyfundmanager : "employs"

    scheme_master ||--|| scheme_details : "detailed_by"
    scheme_details ||--o{ currentnav : "has_nav"
    scheme_details ||--o{ navhist : "nav_history"
    scheme_details ||--o{ mf_return : "performance"
    scheme_details ||--o{ mf_portfolio : "holds"
    scheme_details ||--o{ scheme_aum : "aum_data"
    scheme_details ||--o{ mf_sip : "sip_options"
    scheme_details ||--o{ mf_swp : "swp_options"
    scheme_details ||--o{ mf_stp : "stp_options"
    scheme_details ||--o{ scheme_index_part : "benchmarked_against"
    scheme_details ||--o{ divdetails : "dividend_history"
    scheme_details ||--o{ schemeload : "load_structure"
    scheme_details ||--o{ mf_ratio : "risk_metrics"
    scheme_details ||--o{ expenceratio : "expense_data"

    type_mst ||--o{ scheme_details : "categorizes"
    option_mst ||--o{ scheme_details : "option_type"
    sclass_mst ||--o{ scheme_details : "asset_class"
    rt_mst ||--o{ scheme_details : "registrar"
    plan_mst ||--o{ scheme_details : "plan_type"

    fundmanager_mst ||--o{ dailyfundmanager : "manages_schemes"

    companymaster ||--o{ mf_portfolio : "held_in_portfolio"
    companymaster ||--|| industry_mst : "belongs_to_industry"

    asect_mst ||--o{ mf_portfolio : "asset_sector"

    index_mst ||--o{ scheme_index_part : "benchmark_index"
    index_mst ||--|| companymaster : "index_constituent"
    index_mst ||--o{ bm_annualisedreturn : "performance_data"

    div_mst ||--o{ divdetails : "dividend_type"
    loadtype_mst ||--o{ schemeload : "load_type"
