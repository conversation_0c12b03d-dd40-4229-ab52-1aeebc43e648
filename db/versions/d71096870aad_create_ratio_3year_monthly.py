"""create ratio_3year_monthly

Revision ID: d71096870aad
Revises: 6ac861736385
Create Date: 2025-06-15 11:26:20.660054

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "d71096870aad"
down_revision: Union[str, None] = "6ac861736385"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "ratio_3year_monthlyret",
        sa.Column(
            "schemecode",
            sa.BigInteger(),  # Defined as Integer in source [1]
            nullable=False,
        ),
        sa.Column(
            "ratiodate",
            sa.DateTime(),  # Defined as Datetime in source [1]
            nullable=False,
        ),
        sa.Column(
            "average_nav", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "sd_nav", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "semisd", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "beta", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "corel", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "betacorel", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "rsq", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "trey", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "fama", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "sharpe", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "jalpha", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "sortino", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [1]
        sa.Column(
            "retdueimp", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "retduesel", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "downsideprob", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "downsiderisk", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "sortinosd", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "trackingerror", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "informationratio", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "sdann", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "avgindex", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "sd_index", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "covar", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "maxret", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "minret", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "rfr", sa.Numeric(18, 6), nullable=True
        ),  # Defined as Float in source [2]
        sa.Column(
            "priceindex", sa.BigInteger(), nullable=True
        ),  # Defined as Integer in source [2]
        sa.Column(
            "priceindexname",
            sa.String(length=255),  # Defined as Varchar(255) in source [2]
            nullable=True,
        ),
        sa.Column(
            "flag", sa.String(length=1), nullable=True
        ),  # Defined as Varchar(1) in source [2]
        sa.PrimaryKeyConstraint(
            "schemecode", "ratiodate"
        ),  # Composite Primary Key based on interpretation
    )
    # Commit the table creation to make it visible for subsequent operations, especially for concurrent index creation.
    op.execute("COMMIT;")

    # Create indexes for frequently queried columns to improve performance
    op.create_index(
        index_name="ix_ratio_3year_monthlyret_schemecode",
        table_name="ratio_3year_monthlyret",
        columns=["schemecode"],
        postgresql_concurrently=True,  # Allows index creation without locking the table
    )
    op.create_index(
        index_name="ix_ratio_3year_monthlyret_ratiodate",
        table_name="ratio_3year_monthlyret",
        columns=["ratiodate"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop the table, which also drops associated indexes.
    op.drop_table("ratio_3year_monthlyret")
