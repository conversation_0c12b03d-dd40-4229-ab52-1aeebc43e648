import warnings

warnings.filterwarnings("ignore", category=UserWarning, module="pydantic")

from fastapi import FastAP<PERSON>
from fastapi.openapi.utils import get_openapi
from src.web_app import initialize_web_app

api_description: str = """# Kairo API V 1.0.0
## API Base URL

```
https://api.quantsnap.io/
```

A Sample endpoint would look like

```
GET https://api.quantsnap.io/k/api/v1/healthcheck

OR 

GET https://api.quantsnap.io/k/up
```
Click the API URL under each section to view the full URL required. 


## Response Object

The API response payloads below show only the primary resource object in consideration. The resource is 
wrapped in a response wrapper as described below

```json
{
  "data": {}, 
  "api_version": "v1.0"
  "errors": {
    "code": null,
    "msg": []
  }
}
```

So for example a successfull response with status `200 OK` will look like this

```json
{
    "data": {
        "request_id": "aaf1cc29-2068-4539-b677-3a1617668e21",
        "timestamp": "2023-10-01T12:00:00Z"
    },
    "api_version": "1.0",
    "errors": {
        "msg": [],
        "code": null
    }
}
```

And a request that returns a `401 Unauthorized` would look like this

```json
{
	"data": {
		"request_id": "aaf1cc29-2068-4539-b677-3a1617668e21",
		"error_id": "c9255b21-37cb-4a5c-a7fb-c03f66702559"
	},
	"api_version": "v1.0",
	"errors": {
		"msg": [
			"Error unauthorized"
		],
		"code": "error_unauthorized"
	}
}
```

Notice, the primary response schema remains consistent with the `data` attribute populated when the resource 
values exist. The `errors` object contains a `msg` attribute which usually is a human readable message and 
the `code` attribute which describes the error code.
"""


app = FastAPI(
    title="kairo",
    swagger_ui_parameters={"syntaxHighlight.theme": "obsidian"},
    redoc_url="/k/_redoc",
)


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="Kairo",
        version="1.0.0",
        description=api_description,
        routes=app.routes,
    )
    openapi_schema["info"]["x-logo"] = {
        # "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi
app = initialize_web_app(app=app)
