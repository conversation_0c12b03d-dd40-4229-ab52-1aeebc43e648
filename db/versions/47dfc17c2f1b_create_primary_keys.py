"""create_primary_keys

Revision ID: 47dfc17c2f1b
Revises: 9de9fda41765
Create Date: 2025-06-14 19:09:22.021398

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "47dfc17c2f1b"
down_revision: Union[str, None] = "9de9fda41765"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    ### Add primary key constraint on amc_paum on amc_code and amc_date
    op.create_primary_key(
        constraint_name="pk_amc_paum",
        table_name="amc_paum",
        columns=["amc_code", "aumdate"],
    )
    op.create_primary_key(
        constraint_name="pk_mf_return",
        table_name="mf_return",
        columns=["schemecode"],
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_constraint(
        constraint_name="pk_amc_paum",
        table_name="amc_paum",
        type_="primary",
    )
    op.drop_constraint(
        constraint_name="pk_mf_return",
        table_name="mf_return",
        type_="primary",
    )
