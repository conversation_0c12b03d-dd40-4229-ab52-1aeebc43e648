"""create_dailyfundmanager

Revision ID: 59f1c442ca5a
Revises: 9c90a3de0af2
Create Date: 2025-04-27 10:59:10.573600

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "59f1c442ca5a"
down_revision: Union[str, None] = "9c90a3de0af2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    Creates the DailyFundmanager table based on the provided schema.
    This table contains monthly fund manager codes data.
    For the latest details, refer to the Scheme_details table.
    """
    op.create_table(
        "dailyfundmanager",
        sa.Column("date", sa.DateTime, primary_key=True, comment="Date"),  # [1]
        sa.Column(
            "amc",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s AMC Code is unique for each company",
        ),  # [1]
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Schemecode",
        ),  # [1]
        sa.Column(
            "fundmanger1", sa.BigInteger, nullable=True, comment="fund manager1 code"
        ),  # [2]
        sa.Column(
            "fundmanger2", sa.BigInteger, nullable=True, comment="fund manager2 code"
        ),  # [2]
        sa.Column(
            "fundmanger3", sa.BigInteger, nullable=True, comment="fund manager3 code"
        ),  # [2]
        sa.Column(
            "fundmanger4", sa.BigInteger, nullable=True, comment="fund manager4 code"
        ),  # [2]
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),  # [2]
        # Primary key is composite: date, amc, and schemecode based on the source [1]
        sa.PrimaryKeyConstraint("date", "amc", "schemecode"),
    )


def downgrade():
    """
    Drops the DailyFundmanager table.
    """
    op.drop_table("dailyfundmanager", if_exists=True)
