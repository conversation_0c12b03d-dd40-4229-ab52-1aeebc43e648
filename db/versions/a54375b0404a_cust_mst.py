"""cust_mst

Revision ID: a54375b0404a
Revises: 3ac22390b255
Create Date: 2025-03-09 16:54:23.954743

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "a54375b0404a"
down_revision: Union[str, None] = "3ac22390b255"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "cust_mst",
        sa.Column("cust_code", sa.Integer(), primary_key=True, nullable=False),
        sa.Column("cust_name", sa.String(length=100), nullable=True),
        sa.Column("sebi_reg_no", sa.String(length=25), nullable=True),
        sa.Column("add1", sa.Text(), nullable=True),
        sa.Column("add2", sa.Text(), nullable=True),
        sa.Column("add3", sa.Text(), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="cust_mst_cust_code_idx",
        table_name="cust_mst",
        columns=["cust_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="cust_mst_cust_name_idx",
        table_name="cust_mst",
        columns=["cust_name"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="cust_mst_sebi_reg_no_idx",
        table_name="cust_mst",
        columns=["sebi_reg_no"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("cust_mst")
