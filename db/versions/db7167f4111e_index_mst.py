"""index_mst

Revision ID: db7167f4111e
Revises: e6c3abc4b539
Create Date: 2025-03-09 17:19:51.006301

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "db7167f4111e"
down_revision: Union[str, None] = "e6c3abc4b539"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "index_mst",
        sa.Column("indexcode", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("fincode", sa.BigInteger(), nullable=True),
        sa.Column("scripcode", sa.BigInteger(), nullable=True),
        sa.Column("indexname", sa.String(length=255), nullable=True),
        sa.Column("index_gp", sa.String(length=250), nullable=True),
        sa.Column("subgroup", sa.String(length=250), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="index_mst_indexcode_idx",
        table_name="index_mst",
        columns=["indexcode"],
        unique=True,
    )
    op.create_index(
        index_name="index_mst_fincode_idx",
        table_name="index_mst",
        columns=["fincode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="index_mst_scripcode_idx",
        table_name="index_mst",
        columns=["scripcode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("index_mst")
