"""create_plan_mst

Revision ID: 3ac22390b255
Revises: cc980b69beb5
Create Date: 2025-03-09 16:49:33.245616

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "3ac22390b255"
down_revision: Union[str, None] = "cc980b69beb5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "plan_mst",
        sa.Column("plan_code", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("plan", sa.String(length=50), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="plan_mst_plan_code_idx",
        table_name="plan_mst",
        columns=["plan_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="plan_mst_plan_idx",
        table_name="plan_mst",
        columns=["plan"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("plan_mst")
