from contextvars import Con<PERSON>Var
from uuid import UUID
from typing import Callable, Final, Optional
from fastapi.requests import Request
from starlette.types import <PERSON>GI<PERSON>pp
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.requests import Request
from pydantic import BaseModel, Field, ConfigDict
from contextvars import ContextVar
from typing import Final, Optional
from uuid import uuid4, UUID
from src.domains.user.dao import UserDAO

TRACE_ID_CTX_KEY: Final[str] = "trace_id"
REQUEST_ID_CTX_KEY: Final[str] = "request_id"
_request_id_ctx_var: ContextVar[Optional[str]] = ContextVar(
    REQUEST_ID_CTX_KEY, default=None
)
_trace_id_ctx_var: ContextVar[Optional[str]] = ContextVar(
    TRACE_ID_CTX_KEY, default=None
)


async def _set_request_id() -> str:
    rid: str = str(uuid4())
    _request_id_ctx_var.set(rid)
    return rid


async def _set_trace_id() -> str:
    tid: str = str(uuid4())
    _trace_id_ctx_var.set(tid)
    return tid


class RequestCtxDAO(BaseModel):
    request_id: Optional[UUID | str] = None
    trace_id: Optional[UUID | str] = None
    path: Optional[str] = None
    request_ip: Optional[str] = None
    current_user: Optional[UserDAO] = Field(default=None)
    model_config = ConfigDict(
        title="request_ctx",
        from_attributes=True,
        arbitrary_types_allowed=True,
        json_encoders={
            UUID: lambda o: str(o),
        },
    )


def _get_request_id():
    return _request_id_ctx_var.get()


def _get_trace_id():
    return _trace_id_ctx_var.get()


REQUEST_CTX_KEY: Final[str] = "ctx"
_request_ctx: ContextVar[Optional[RequestCtxDAO]] = ContextVar(
    REQUEST_CTX_KEY, default=RequestCtxDAO()
)


async def _set_request_ctx(request: Request) -> ContextVar[Optional[RequestCtxDAO]]:
    _rid: str = await _set_request_id()
    _tid: str = await _set_trace_id()
    _rip: str = request.headers.get("x-forwarded-for", "")
    _ctx: RequestCtxDAO = RequestCtxDAO(
        request_id=_rid, path=request.base_url.path, request_ip=_rip, trace_id=_tid
    )
    _request_ctx.set(_ctx)
    request.state.ctx = _ctx
    return _request_ctx


async def _get_request_ctx() -> Optional[RequestCtxDAO]:
    return _request_ctx.get()


class RequestCtxMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable):
        await _set_request_ctx(request=request)
        response = await call_next(request)
        return response
