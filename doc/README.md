# Kairo Mutual Fund Database Documentation

This directory contains comprehensive documentation for the Kairo mutual fund database system, designed to help developers understand the complex mutual fund domain and database structure.

## Documentation Files

### 📊 [Entity Relationship Diagram (ERD)](mutual_fund_erd.mmd)
- **File**: `mutual_fund_erd.mmd`
- **Format**: Mermaid diagram
- **Purpose**: Visual representation of all database tables, relationships, and key fields
- **Usage**: Can be rendered in GitHub, GitLab, or any Mermaid-compatible viewer

**Key Features:**
- 50+ database tables organized by functional groups
- Primary and foreign key relationships
- Core business entities and their connections
- Master data and classification tables

### 📚 [Developer Guide](mutual_fund_developer_guide.md)
- **File**: `mutual_fund_developer_guide.md`
- **Format**: Comprehensive Markdown documentation
- **Purpose**: Complete guide for developers with limited mutual fund domain knowledge

**Contents:**
- Mutual fund industry concepts explained in simple terms
- Database architecture overview
- Detailed table descriptions with business context
- Common query patterns and examples
- Data flow workflows
- Technical implementation notes
- Comprehensive glossary

### 🗃️ [Database Schema](kairo_development.sql)
- **File**: `kairo_development.sql`
- **Format**: PostgreSQL DDL
- **Purpose**: Complete database schema with table definitions, indexes, and comments

## Quick Start Guide

### For New Developers

1. **Start with the basics**: Read the "Mutual Fund Domain Concepts" section in the Developer Guide
2. **Understand the structure**: Review the ERD to see how tables relate to each other
3. **Dive into details**: Use the Developer Guide for specific table explanations
4. **Practice with queries**: Try the example queries provided in the documentation

### For Database Administrators

1. **Schema Review**: Examine the complete SQL schema file
2. **Performance Optimization**: Review the indexing strategy documented in the ERD
3. **Data Relationships**: Understand foreign key constraints and referential integrity

### For Business Analysts

1. **Business Context**: Focus on the "Key Business Workflows" section
2. **Data Lineage**: Understand how data flows through the system
3. **Reporting Queries**: Use the common query patterns as starting points

## Database Overview

The Kairo database models the complete mutual fund ecosystem with these core entity groups:

### 🏢 AMC & Fund Management
- Asset Management Companies (AMCs)
- Mutual fund schemes and their details
- Fund manager information and assignments

### 📈 Performance & Analytics
- Daily NAV (Net Asset Value) calculations
- Historical performance data
- Risk metrics and ratios
- Benchmark comparisons

### 💰 Transactions & Holdings
- SIP (Systematic Investment Plan) configurations
- SWP (Systematic Withdrawal Plan) options
- STP (Systematic Transfer Plan) settings
- Portfolio holdings and asset allocation

### 📋 Master Data
- Fund classifications and categories
- Investment options and plan types
- Registrar and transfer agent information
- Industry and sector classifications

### 💹 Market Data
- Index and benchmark information
- Company master data
- Market capitalization data
- Dividend and corporate action details

## Key Business Concepts

### NAV (Net Asset Value)
The per-unit price of a mutual fund, calculated daily based on the market value of underlying assets.

### SIP/SWP/STP
- **SIP**: Systematic Investment Plan - Regular monthly investments
- **SWP**: Systematic Withdrawal Plan - Regular monthly withdrawals  
- **STP**: Systematic Transfer Plan - Regular transfers between schemes

### AUM (Assets Under Management)
Total value of assets managed by a fund or AMC, indicating size and investor confidence.

### Risk Metrics
- **Beta**: Volatility relative to market benchmark
- **Alpha**: Excess returns over benchmark
- **Sharpe Ratio**: Risk-adjusted returns
- **Standard Deviation**: Measure of volatility

## Data Quality & Governance

### Common Patterns
- **Flag Fields**: All tables include `flag` fields for soft deletes and status tracking
- **Audit Trail**: Timestamp fields for tracking data updates
- **Composite Keys**: Many tables use (schemecode, date) combinations for time-series data

### Data Validation
- Extensive foreign key relationships ensure referential integrity
- Regular validation processes needed for financial data accuracy
- SEBI compliance requirements for data retention and reporting

## Technical Notes

### Performance Considerations
- Heavy indexing on scheme codes and date fields
- Consider partitioning for large historical tables
- Read replicas recommended for analytics workloads

### Data Types
- Financial amounts use `numeric(18,6)` for precision
- Codes use integer types for efficient joins
- Flags use single character fields for status indicators

## Getting Help

### For Technical Issues
- Review the detailed table descriptions in the Developer Guide
- Check the ERD for relationship understanding
- Consult the SQL schema for exact field definitions

### For Business Logic Questions
- Refer to the glossary for mutual fund terminology
- Review the business workflow sections
- Consult with domain experts for regulatory requirements

### For Performance Questions
- Review indexing strategies in the schema
- Consider the data volume patterns described
- Evaluate partitioning strategies for time-series data

---

## Contributing

When updating this documentation:

1. **Keep it current**: Update documentation when schema changes
2. **Add examples**: Include practical query examples for new features
3. **Explain business context**: Always include why, not just what
4. **Update the ERD**: Reflect any new tables or relationships

---

*Last Updated: July 2025*
*Database Version: kairo_development*
*Documentation Version: 1.0*
