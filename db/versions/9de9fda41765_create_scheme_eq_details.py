"""create_scheme_eq_details

Revision ID: 9de9fda41765
Revises: b43602dd40ee
Create Date: 2025-04-27 12:18:39.877852

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "9de9fda41765"
down_revision: Union[str, None] = "b43602dd40ee"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "scheme_eq_details",  # Table name in lowercase as requested
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            nullable=False,
            comment="Accord Fintech’s Schemecode",
        ),  # BigInteger as requested
        sa.Column(
            "monthend", sa.BigInteger(), nullable=False, comment="Month End"
        ),  # BigInteger as requested
        sa.Column(
            "mcap", sa.Numeric(precision=18, scale=6), comment="Market Cap"
        ),  # Numeric(18, 6) as requested
        sa.Column(
            "pe", sa.Numeric(precision=18, scale=6), comment="PE Ratio"
        ),  # Numeric(18, 6) as requested
        sa.Column(
            "pb", sa.Numeric(precision=18, scale=6), comment="PB Ratio"
        ),  # Numeric(18, 6) as requested
        sa.Column(
            "div_yield", sa.Numeric(precision=18, scale=6), comment="Div Yield"
        ),  # Numeric(18, 6) as requested
        sa.Column(
            "flag", sa.String(length=1), comment="Updation Flag"
        ),  # String(1) for Varchar(1)
        # Defining a composite primary key on schemecode and monthend for uniqueness per month
        sa.PrimaryKeyConstraint("schemecode", "monthend"),
        # Consider adding indexes if needed for performance, e.g.:
        # sa.Index('idx_schemecode', 'schemecode'),
        # sa.Index('idx_monthend', 'monthend')
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("scheme_eq_details")
