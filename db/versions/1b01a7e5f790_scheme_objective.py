"""scheme_objective

Revision ID: 1b01a7e5f790
Revises: e9c1d0e28930
Create Date: 2025-03-09 17:00:57.135434

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "1b01a7e5f790"
down_revision: Union[str, None] = "e9c1d0e28930"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "scheme_objective",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            primary_key=True,
            nullable=False,
        ),
        sa.Column("objective", sa.Text(), nullable=True),  # ntext -> Text
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="scheme_objective_schemecode_idx",
        table_name="scheme_objective",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("scheme_objective")
