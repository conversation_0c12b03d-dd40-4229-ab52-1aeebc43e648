"""create_mf_stp

Revision ID: 7544249818c5
Revises: a5182891f85d
Create Date: 2025-03-09 17:10:26.239356

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "7544249818c5"
down_revision: Union[str, None] = "a5182891f85d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "mf_stp",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column(
            "amc_code",
            sa.BigInteger(),
            nullable=True,
        ),
        sa.Column("frequency", sa.String(length=100), nullable=False),
        sa.Column("stpinout", sa.String(length=1), nullable=False),
        sa.Column("stp", sa.String(length=1), nullable=True),
        sa.Column("stpdatescondition", sa.String(length=8000), nullable=True),
        sa.Column("dates", sa.String(length=8000), nullable=True),
        sa.Column("stpdaysall", sa.String(length=50), nullable=True),
        sa.Column("stpmininvest", sa.Numeric(18, 6), nullable=True),
        sa.Column("stpaddninvest", sa.Numeric(18, 6), nullable=True),
        sa.Column("stpfrequencyno", sa.Integer(), nullable=True),
        sa.Column("stpminimumperiod", sa.Integer(), nullable=True),
        sa.Column("stpmaximumperiod", sa.String(length=100), nullable=True),
        sa.Column("stpmincumamount", sa.String(length=100), nullable=True),
        sa.Column("stpminunits", sa.Numeric(18, 6), nullable=True),
        sa.Column("stpmultiplesunits", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "frequency", "stpinout"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="mf_stp_schemecode_idx",
        table_name="mf_stp",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_stp_amc_code_idx",
        table_name="mf_stp",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("mf_stp")
