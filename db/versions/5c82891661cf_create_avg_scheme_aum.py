"""create_avg_scheme_aum

Revision ID: 5c82891661cf
Revises: 9981b5a68678
Create Date: 2025-03-09 17:49:38.059898

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "5c82891661cf"
down_revision: Union[str, None] = "9981b5a68678"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "avg_scheme_aum",
        sa.Column("schemecode", sa.BigInteger(), nullable=False),
        sa.Column("date", sa.DateTime(), nullable=False),
        sa.Column("exfof", sa.Numeric(18, 6), nullable=True),
        sa.<PERSON>umn("fof", sa.Numeric(18, 6), nullable=True),
        sa.Column("total", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.<PERSON>eyConstraint("schemecode", "date"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="avg_scheme_aum_schemecode_idx",
        table_name="avg_scheme_aum",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("avg_scheme_aum")
