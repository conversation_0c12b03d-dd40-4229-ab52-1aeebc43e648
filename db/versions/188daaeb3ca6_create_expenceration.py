"""create_expenceration

Revision ID: 188daaeb3ca6
Revises: 01aa7ccabf4f
Create Date: 2025-04-27 11:06:45.883732

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "188daaeb3ca6"
down_revision: Union[str, None] = "01aa7ccabf4f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the Expenceratio table based on the provided schema.
    This table contains scheme expense ratio data.
    For the latest information, refer to the latest date for a scheme.
    """
    op.create_table(
        "expenceratio",
        sa.Column(
            "amc_code",
            sa.BigInteger,
            nullable=True,
            comment="Accord Fintech’s AMC Code is unique for each company",
        ),  # [2]
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Schemecode",
        ),  # [2]
        sa.Column("date", sa.DateTime, primary_key=True, comment="Date"),  # [2]
        sa.Column(
            "expratio", sa.Numeric(18, 6), nullable=True, comment="Expense Ratio"
        ),  # [2]
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),  # [2]
        # Primary key is composite: schemecode and date based on the source
        sa.PrimaryKeyConstraint("schemecode", "date"),  # [2]
    )


def downgrade():
    """
    Drops the Expenceratio table.
    """
    op.drop_table("expenceratio")
