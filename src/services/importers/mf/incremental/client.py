from typing import Optional
from os import Path<PERSON>ike
from config.cfg import cfg
from config.logger import log_info, log_error
from aiohttp import ClientSession, BasicAuth

from asyncio import Semaphore, Task, gather


class ImporterClient:
    base_url: str | PathLike = cfg("MF_IMPORTER_URL")
    token: str = cfg("MF_IMPORTER_TOKEN")
    proxy_url: str = cfg("PROXY_URL")
    proxy_username: str = cfg("PROXY_USERNAME")
    proxy_password: str = cfg("PROXY_PASSWORD")

    @classmethod
    async def get_in_batches(
        cls,
        import_file_list: list["ImportFileInfo"],
        concurrent_count: int = 3,
        with_proxy: bool = False,
    ) -> Optional[list[dict | None]]:
        """Fetches multiple files concurrently from the MF Importer service.

        Args:
            tasks (list[Task]): A list of asyncio tasks to fetch files.
            semaphore (Semaphore
        """

        if concurrent_count > 1:
            semaphore: Semaphore = Semaphore(concurrent_count)
        else:
            semaphore = Semaphore(1)

        async with ClientSession() as session:
            results: list[Optional[dict]] = [
                {
                    f"{import_file_info.filename }": await cls._fetch_with_semaphore(
                        session=session,
                        import_file_info=import_file_info,
                        semaphore=semaphore,
                        with_proxy=with_proxy,
                    )
                }
                for import_file_info in import_file_list
            ]
        return results

    @classmethod
    async def _fetch_with_semaphore(
        cls,
        session: ClientSession,
        import_file_info: "ImportFileInfo",
        semaphore: Semaphore,
        with_proxy: bool = False,
    ) -> Optional[dict]:
        """Fetches a file with a semaphore to limit concurrent requests.

        Args:
            session (ClientSession): The aiohttp session to use for the request.
            import_file_info (ImportFileInfo): An instance containing file details.

        Returns:
            Optional[dict]: Returns a dictionary containing the file data if successful, otherwise None.
        """
        url: str = (
            f"{cls.base_url}?filename={import_file_info.filename}&sub={import_file_info.sub}&section={import_file_info.section}&date={import_file_info.current_date}&token={cls.token}"
        )

        kwargs: dict = {"raise_for_status": False}

        if with_proxy:
            kwargs["proxy"] = cfg("PROXY_URL")
            if cfg("PROXY_USERNAME") and cfg("PROXY_PASSWORD"):
                kwargs["proxy_auth"] = BasicAuth(
                    login=cls.proxy_username,
                    password=cls.proxy_password,
                )

        async with semaphore:
            log_info(msg=f"Fetching {import_file_info.filename} from {url}")
            try:
                async with session.get(url, **kwargs) as response:
                    if response.status >= 200 and response.status < 300:
                        log_info(
                            msg=f"Successfully fetched {import_file_info.filename}"
                        )
                        return await response.json()
                    else:
                        log_error(
                            msg=f"Failed to fetch {import_file_info.filename}: {response.status}"
                        )
                        return None
            except Exception as e:
                log_error(msg=f"Error fetching {import_file_info.filename}: {e}")
                return None

    @classmethod
    async def get(
        cls,
        import_file_info: "ImportFileInfo",
        with_proxy: bool = False,
    ) -> Optional[dict]:
        """Fetches a file from the MF Importer service.

        Args:
            filename (str): The name of the file to fetch.
            date (str): The date associated with the file, typically in 'YYYYMMDD' format.
            section (str): A string value one of MFMaster, MFNav, or MFOther
            sub (Optional[str], optional): Never populated. Defaults to an empty string.

        Returns:
            Optional[dict]: Returns a dictionary containing the file data if successful, otherwise None.
        """
        # Implementation of fetching logic goes here
        url: str = (
            f"{cls.base_url}?filename={import_file_info.filename}&sub={import_file_info.sub}&section={import_file_info.section}&date={import_file_info.current_date}&token={cls.token}"
        )

        kwargs: dict = {"raise_for_status": False}

        if with_proxy:
            kwargs["proxy"] = cfg("PROXY_URL")
            if cfg("PROXY_USERNAME") and cfg("PROXY_PASSWORD"):
                kwargs["proxy_auth"] = BasicAuth(
                    login=cls.proxy_username,
                    password=cls.proxy_password,
                )

        try:
            async with ClientSession() as session:

                log_info(msg=f"Fetching {import_file_info.filename} from {url}")
                async with session.get(url, **kwargs) as response:
                    if response.status >= 200 and response.status < 300:
                        log_info(
                            msg=f"Successfully fetched {import_file_info.filename}"
                        )
                        return await response.json()
                    else:
                        log_error(
                            msg=f"Failed to fetch {import_file_info.filename}: {response.status}"
                        )
                        return None
        except Exception as e:
            log_error(msg=f"Error fetching {import_file_info.filename}: {e}")
            return None
