version: "3"
services:
  api:
    image: registry.gitlab.com/aidolabs/quantsnap/kairo/kairo_api:latest
    build:
      context: ./..
      dockerfile: docker/app/Dockerfile
    ports:
      - "0.0.0.0:9005:9005"
    env_file:
      - ./../../files/data/prod.env
    command: ["./launch.sh"]
    deploy:
      mode: replicated
      replicas: 1
      update_config:
        parallelism: 1
        delay: 5s
        order: start-first
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.id == ncgfmfxt0dwyte86hnnwlwk2j
    healthcheck:
      test: ["CMD", "curl", "-f", "http://0.0.0.0:9005/k/up"]
      interval: 15s
      timeout: 30s
      retries: 5
      start_period: 30s
    networks:
      - dokploy-network
    volumes:
      - /mnt/metabase_volume/local_data/:/app/local_data/
    hostname: api
    labels:
      - traefik.enable=true
  workers:
    image: registry.gitlab.com/aidolabs/quantsnap/kairo/kairo_api:latest
    build:
      context: ./..
      dockerfile: docker/app/Dockerfile
    env_file:
      - ./../../files/data/prod.env
    command: ["./launch_celery.sh"]
    deploy:
      mode: replicated
      replicas: 1
      update_config:
        parallelism: 1
        delay: 5s
        order: start-first
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.id == ncgfmfxt0dwyte86hnnwlwk2j
    networks:
      - dokploy-network
    volumes:
      - /mnt/metabase_volume/local_data/:/app/local_data/
    hostname: workers

networks:
  dokploy-network:
    external: true
