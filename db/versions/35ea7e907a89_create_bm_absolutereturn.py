"""create_bm_absolutereturn

Revision ID: 35ea7e907a89
Revises: befe4c3aeca7
Create Date: 2025-04-26 14:05:37.774594

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "35ea7e907a89"
down_revision: Union[str, None] = "befe4c3aeca7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "bm_absolutereturn",
        sa.Column(
            "index_code",
            sa.BIGINT,
            primary_key=True,
            comment="Accord Fintech’s Index Code",
        ),  # [1]
        sa.Column(
            "symbol", sa.VARCHAR(255), nullable=True, comment="NSE Symbol Code"
        ),  # [1] - Length inferred as not specified in source
        sa.Column(
            "scripcode", sa.Integer, nullable=True, comment="BSE Scrip code"
        ),  # [2]
        sa.Column("date", sa.DateTime, nullable=True, comment="Current Date"),  # [2]
        sa.Column(
            "prev_date", sa.DateTime, nullable=True, comment="Previous Date"
        ),  # [2]
        sa.Column("close", sa.Numeric(18, 6), nullable=True, comment="Close"),  # [2]
        sa.Column(
            "prev_close", sa.Numeric(18, 6), nullable=True, comment="Previous Close"
        ),  # [2]
        sa.Column(
            "1dayret", sa.Numeric(18, 6), nullable=True, comment="One Day Return"
        ),  # [2]
        sa.Column(
            "1weekdate", sa.DateTime, nullable=True, comment="One Week Date"
        ),  # [2]
        sa.Column(
            "1weekclose", sa.Numeric(18, 6), nullable=True, comment="One Week Close"
        ),  # [2]
        sa.Column(
            "1weekret", sa.Numeric(18, 6), nullable=True, comment="One Week Return"
        ),  # [2]
        sa.Column(
            "1mthdate", sa.DateTime, nullable=True, comment="One Month Date"
        ),  # [2]
        sa.Column(
            "1mthclose", sa.Numeric(18, 6), nullable=True, comment="One Month Close"
        ),  # [2]
        sa.Column(
            "1monthret", sa.Numeric(18, 6), nullable=True, comment="One Month Return"
        ),  # [2]
        sa.Column(
            "3mthdate", sa.DateTime, nullable=True, comment="Three Month Date"
        ),  # [3]
        sa.Column(
            "3mthclose", sa.Numeric(18, 6), nullable=True, comment="Three Month Close"
        ),  # [3]
        sa.Column(
            "3monthret", sa.Numeric(18, 6), nullable=True, comment="Three Month Return"
        ),  # [3]
        sa.Column(
            "6mntdate", sa.DateTime, nullable=True, comment="Six Month Date"
        ),  # [3]
        sa.Column(
            "6mnthclose", sa.Numeric(18, 6), nullable=True, comment="Six Month Close"
        ),  # [3]
        sa.Column(
            "6monthret", sa.Numeric(18, 6), nullable=True, comment="Six Month Return"
        ),  # [3]
        sa.Column(
            "9mnthdate", sa.DateTime, nullable=True, comment="Nine Month Date"
        ),  # [3]
        sa.Column(
            "9mnthclose", sa.Numeric(18, 6), nullable=True, comment="Nine Month Close"
        ),  # [3]
        sa.Column(
            "9mnthret", sa.Numeric(18, 6), nullable=True, comment="Nine Month Return"
        ),  # [3]
        sa.Column(
            "1yrdate", sa.DateTime, nullable=True, comment="One Year Date"
        ),  # [3]
        sa.Column(
            "1yrclose", sa.Numeric(18, 6), nullable=True, comment="One Year Close"
        ),  # [3]
        sa.Column(
            "1yrret", sa.Numeric(18, 6), nullable=True, comment="One Year Return"
        ),  # [3]
        sa.Column(
            "2yrdate", sa.DateTime, nullable=True, comment="Two Year Date"
        ),  # [4]
        sa.Column(
            "2yrclose", sa.Numeric(18, 6), nullable=True, comment="Two Year Close"
        ),  # [4]
        sa.Column(
            "2yearret", sa.Numeric(18, 6), nullable=True, comment="Two Year Return"
        ),  # [4]
        sa.Column(
            "3yrdate", sa.DateTime, nullable=True, comment="Three Year Date"
        ),  # [4]
        sa.Column(
            "3yrclose", sa.Numeric(18, 6), nullable=True, comment="Three Year Close"
        ),  # [4]
        sa.Column(
            "3yearret", sa.Numeric(18, 6), nullable=True, comment="Three Year Return"
        ),  # [4]
        sa.Column(
            "4yrdate", sa.DateTime, nullable=True, comment="Four Year Date"
        ),  # [4]
        sa.Column(
            "4yrclose", sa.Numeric(18, 6), nullable=True, comment="Four Year Close"
        ),  # [4]
        sa.Column(
            "4yearret", sa.Numeric(18, 6), nullable=True, comment="Four Year Return"
        ),  # [4]
        sa.Column(
            "5yrdate", sa.DateTime, nullable=True, comment="Five Year Date"
        ),  # [4]
        sa.Column(
            "5yrclose", sa.Numeric(18, 6), nullable=True, comment="Five Year Close"
        ),  # [4]
        sa.Column(
            "5yearret", sa.Numeric(18, 6), nullable=True, comment="Five Year Return"
        ),  # [4]
        sa.Column(
            "incdate", sa.DateTime, nullable=True, comment="Inception date"
        ),  # [4]
        sa.Column(
            "incclose",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Close on inception date",
        ),  # [4]
        sa.Column(
            "incret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return on inception date",
        ),  # [4]
        sa.Column(
            "2weekdate", sa.DateTime, nullable=True, comment="Two Week Date"
        ),  # [4]
        sa.Column(
            "2weekclose", sa.Numeric(18, 6), nullable=True, comment="Two Week Close"
        ),  # [5]
        sa.Column(
            "2weekret", sa.Numeric(18, 6), nullable=True, comment="Two Week Return"
        ),  # [5]
        sa.Column(
            "3weekdate", sa.DateTime, nullable=True, comment="Three Week Date"
        ),  # [5]
        sa.Column(
            "3weekclose", sa.Numeric(18, 6), nullable=True, comment="Three Week Close"
        ),  # [5]
        sa.Column(
            "3weekret", sa.Numeric(18, 6), nullable=True, comment="Three Week Return"
        ),  # [5]
        sa.Column(
            "2mthdate", sa.DateTime, nullable=True, comment="Two Month Date"
        ),  # [5]
        sa.Column(
            "2mthclose", sa.Numeric(18, 6), nullable=True, comment="Two Month Close"
        ),  # [5]
        sa.Column(
            "2monthret", sa.Numeric(18, 6), nullable=True, comment="Two Month Return"
        ),  # [5]
        sa.Column("ytddate", sa.DateTime, nullable=True, comment="YTD Date"),  # [5]
        sa.Column(
            "ytdclose", sa.Numeric(18, 6), nullable=True, comment="YTD Close"
        ),  # [5]
        sa.Column(
            "ytdret", sa.Numeric(18, 6), nullable=True, comment="YTD Return"
        ),  # [5]
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),  # [5]
        # Note: Based on the source document, 'index_code' is listed as the only Primary Key [1].
        # However, for time-series data that is appended daily, a composite
        # primary key including the date column is typically used to ensure row uniqueness
        # per index per day. The schema as provided in the source
        # only explicitly marks 'index_code' as the PK. This script adheres strictly
        # to the provided PK definition. If daily uniqueness is required, the primary
        # key definition may need adjustment (e.g., PrimaryKeyConstraint('index_code', 'date')).
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("bm_absolutereturn")
