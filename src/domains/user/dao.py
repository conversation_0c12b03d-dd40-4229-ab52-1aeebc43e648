from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from utils.datetime_support import date, datetime
from utils.datetime_support import TIMEZONE_ASIA_KOLKATA, date_to_datetime, asia_kolkata


class UserDAO(BaseModel):
    """The user DAO"""

    model_config = ConfigDict(
        title="user",
        extra="ignore",
        from_attributes=True,
        json_encoders={
            date: lambda v: v.isoformat(),
            datetime: lambda v: v.isoformat(timespec="microseconds"),
        },
    )
    id: Optional[int] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    encrypted_password: Optional[str] = None
    is_blocked: Optional[bool] = False
    timezone: Optional[str] = Field(default=TIMEZONE_ASIA_KOLKATA)
    verified_at: Optional[str] = None
    user_metadata: Optional[dict] = {}
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class UserProfileDAO(BaseModel):
    model_config = ConfigDict(
        title="user_profile",
        extra="ignore",
        from_attributes=True,
        json_encoders={
            date: lambda v: v.isoformat(),
            datetime: lambda v: v.isoformat(timespec="microseconds"),
        },
    )
    id: Optional[int] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: str
    timezone: Optional[str] = Field(default=TIMEZONE_ASIA_KOLKATA)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
