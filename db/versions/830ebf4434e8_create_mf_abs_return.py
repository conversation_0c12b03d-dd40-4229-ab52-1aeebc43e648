"""create_mf_abs_return

Revision ID: 830ebf4434e8
Revises: ad7498b4cb6c
Create Date: 2025-04-27 11:30:23.770994

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "830ebf4434e8"
down_revision: Union[str, None] = "ad7498b4cb6c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the mf_abs_return table based on the provided schema.
    This table contains scheme absolute returns data.
    """
    op.create_table(
        "mf_abs_return",  # Table name in lowercase as requested
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Scheme Code",
        ),  # Using BigInteger as requested, noted as Integer in source [5] but float in [6] - treating as Integer type code
        sa.Column(
            "c_date", sa.DateTime, nullable=True, comment="Closing Date"
        ),  # Datetime from source [6]
        sa.Column(
            "p_date", sa.DateTime, nullable=True, comment="Previous Date"
        ),  # Datetime from source [6]
        sa.Column(
            "c_nav", sa.Numeric(18, 6), nullable=True, comment="Closing Nav"
        ),  # Using Numeric(18,6) for Float from source [6]
        sa.Column(
            "p_nav", sa.Numeric(18, 6), nullable=True, comment="Previous Day Nav"
        ),  # Using Numeric(18,6) for Float from source [6]
        sa.Column(
            "1dayret", sa.Numeric(18, 6), nullable=True, comment="One Day Return"
        ),  # Using Numeric(18,6) for Float from source [6], lowercase and snake_case
        sa.Column(
            "1weekdate", sa.DateTime, nullable=True, comment="One Week Date"
        ),  # Datetime from source [6], lowercase and snake_case
        sa.Column(
            "1weeknav", sa.Numeric(18, 6), nullable=True, comment="One Week NAV"
        ),  # Using Numeric(18,6) for Float from source [6], lowercase and snake_case
        sa.Column(
            "1weekret", sa.Numeric(18, 6), nullable=True, comment="One Week Return"
        ),  # Using Numeric(18,6) for Float from source [6], lowercase and snake_case
        sa.Column(
            "1mthdate", sa.DateTime, nullable=True, comment="One Month Date"
        ),  # Datetime from source [6], lowercase and snake_case
        sa.Column(
            "1mthnav", sa.Numeric(18, 6), nullable=True, comment="One Month NAV"
        ),  # Using Numeric(18,6) for Float from source [7], lowercase and snake_case
        sa.Column(
            "1monthret", sa.Numeric(18, 6), nullable=True, comment="One Month Return"
        ),  # Using Numeric(18,6) for Float from source [7], lowercase and snake_case
        sa.Column(
            "3mthdate", sa.DateTime, nullable=True, comment="3 Month Date"
        ),  # Datetime from source [7], lowercase and snake_case
        sa.Column(
            "3mthnav", sa.Numeric(18, 6), nullable=True, comment="3 Month NAV"
        ),  # Using Numeric(18,6) for Float from source [7], lowercase and snake_case
        sa.Column(
            "3monthret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="3 Month Return",
        ),  # Using Numeric(18,6) for Float from source [7], lowercase and snake_case
        sa.Column(
            "6mntdate", sa.DateTime, nullable=True, comment="Six Month Date"
        ),  # Datetime from source [7], corrected to match field name
        sa.Column(
            "6mnthnav", sa.Numeric(18, 6), nullable=True, comment="Six Month NAV"
        ),  # Using Numeric(18,6) for Float from source [7], corrected to match field name
        sa.Column(
            "6monthret", sa.Numeric(18, 6), nullable=True, comment="Six Month Return"
        ),  # Using Numeric(18,6) for Float from source [7], corrected to match field name
        sa.Column(
            "9mnthdate", sa.DateTime, nullable=True, comment="Nine Month Date"
        ),  # Datetime from source [7], corrected to match field name
        sa.Column(
            "9mnthnav", sa.Numeric(18, 6), nullable=True, comment="Nine Month NAV"
        ),  # Using Numeric(18,6) for Float from source [7], corrected to match field name
        sa.Column(
            "9mnthret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Nine Month Return",
        ),  # Using Numeric(18,6) for Float from source [7], corrected to match field name
        sa.Column(
            "1yrdate", sa.DateTime, nullable=True, comment="One Year Date"
        ),  # Datetime from source [7], corrected to match field name
        sa.Column(
            "1yrnav", sa.Numeric(18, 6), nullable=True, comment="One Year NAV"
        ),  # Using Numeric(18,6) for Float from source [7], corrected to match field name
        sa.Column(
            "1yrret", sa.Numeric(18, 6), nullable=True, comment="One Year Return"
        ),  # Using Numeric(18,6) for Float from source [8], corrected to match field name
        sa.Column(
            "2yrdate", sa.DateTime, nullable=True, comment="2 Year Date"
        ),  # Datetime from source [8], lowercase and snake_case
        sa.Column(
            "2yrnav", sa.Numeric(18, 6), nullable=True, comment="2 Year NAV"
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "2yearret", sa.Numeric(18, 6), nullable=True, comment="2 Year Return"
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "3yrdate", sa.DateTime, nullable=True, comment="3 Year Date"
        ),  # Datetime from source [8], lowercase and snake_case
        sa.Column(
            "3yrnav", sa.Numeric(18, 6), nullable=True, comment="3 Year NAV"
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "3yearret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="3 Year Return",
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "4yrdate", sa.DateTime, nullable=True, comment="4 Year Date"
        ),  # Datetime from source [8], lowercase and snake_case
        sa.Column(
            "4yrnav", sa.Numeric(18, 6), nullable=True, comment="4 Year NAV"
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "4yearret", sa.Numeric(18, 6), nullable=True, comment="4 Year Return"
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "5yrdate", sa.DateTime, nullable=True, comment="5 Year Date"
        ),  # Datetime from source [8], lowercase and snake_case
        sa.Column(
            "5yrnav", sa.Numeric(18, 6), nullable=True, comment="5 Year NAV"
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "5yearret", sa.Numeric(18, 6), nullable=True, comment="5 Year Return"
        ),  # Using Numeric(18,6) for Float from source [8], lowercase and snake_case
        sa.Column(
            "incdate", sa.DateTime, nullable=True, comment="Inception date"
        ),  # Datetime from source [8]
        sa.Column(
            "incnav", sa.Numeric(18, 6), nullable=True, comment="NAV on inception date"
        ),  # Using Numeric(18,6) for Float from source [8]
        sa.Column(
            "incret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return on inception date",
        ),  # Using Numeric(18,6) for Float from source [8]
        sa.Column(
            "flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"
        ),  # Varchar(1) from source [8]
        # Primary key is 'schemecode' based on the source [6]
        sa.PrimaryKeyConstraint("schemecode"),
    )


def downgrade():
    """
    Drops the mf_abs_return table.
    """
    op.drop_table("mf_abs_return", if_exists=True)
