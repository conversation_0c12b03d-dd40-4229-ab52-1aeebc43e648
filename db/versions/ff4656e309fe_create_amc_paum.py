"""create_amc_paum

Revision ID: ff4656e309fe
Revises: 6d8fe2d10cba
Create Date: 2025-03-09 17:38:37.045866

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "ff4656e309fe"
down_revision: Union[str, None] = "6d8fe2d10cba"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "amc_paum",
        sa.Column("amc_code", sa.BigInteger(), nullable=False),
        sa.Column("aumdate", sa.DateTime(), nullable=False),
        sa.Column("totalaum", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="amc_paum_amc_code_idx",
        table_name="amc_paum",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("amc_paum")
