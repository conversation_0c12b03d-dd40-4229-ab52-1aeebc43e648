version: "3"
services:
  api:
    image: kairo_api:latest
    build:
      context: ./..
      dockerfile: docker/app/Dockerfile
    ports:
      - "0.0.0.0:9005:9005"
    env_file:
      - ./../environments/local_stack.env
    command: ["./launch.sh"]
    deploy:
      mode: replicated
      replicas: 1
      update_config:
        parallelism: 1
        delay: 5s
        order: start-first
      restart_policy:
        condition: on-failure
    healthcheck:
      test: ["CMD", "curl", "-f", "http://0.0.0.0:9005/k/up"]
      interval: 15s
      timeout: 30s
      retries: 5
      start_period: 30s
    networks:
      - kairo-app-network
    hostname: api
    labels:
      - traefik.enable=true

networks:
  dokploy-network:
    external: true
  kairo-app-network:
    driver: overlay
    attachable: true
