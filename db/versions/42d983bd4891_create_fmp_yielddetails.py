"""create_fmp_yielddetails

Revision ID: 42d983bd4891
Revises: 188daaeb3ca6
Create Date: 2025-04-27 11:19:05.940101

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "42d983bd4891"
down_revision: Union[str, None] = "188daaeb3ca6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the fmp_yielddetails table based on the provided schema.
    This table contains Fixed Maturity Plan schemes information,
    maintained only for primary schemes.
    """
    op.create_table(
        "fmp_yielddetails",
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Schemecode",
        ),  # [3]
        sa.Column(
            "maturitydate",
            sa.DateTime,
            nullable=True,
            comment="Maturity date of FMP schemes",
        ),  # [3]
        sa.Column(
            "tenure_number",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Schemes tenures in Numbers",
        ),  # [3]
        sa.Column(
            "tenure_option", sa.VARCHAR(10), nullable=True, comment="Schemes Tenure in"
        ),  # [3]
        sa.Column(
            "net_inticative_yield1",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Not available",
        ),  # [4]
        sa.Column(
            "net_inticative_yield2",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Not available",
        ),  # [4]
        sa.Column(
            "post_taxyield_ind1",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Not available",
        ),  # [4]
        sa.Column(
            "post_taxyield_ind2",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Not available",
        ),
        sa.Column(
            "post_taxyield_nonind1",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Not available",
        ),  # [4]
        sa.Column(
            "post_taxyield_nonind2",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Not available",
        ),  # [4]
        sa.Column(
            "exit_load", sa.VARCHAR(30), nullable=True, comment="Exit Load"
        ),  # [4]
        sa.Column(
            "rollover", sa.VARCHAR(1), nullable=True, comment="Roll Over flag"
        ),  # [4]
        sa.Column(
            "maturitydate_after_rollover",
            sa.DateTime,
            nullable=True,
            comment="MaturityDate After Rollover",
        ),  # [4]
        sa.Column(
            "tenure_no_rollover",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Tenure No Rollover",
        ),  # [4]
        sa.Column(
            "tenure_option_rollover",
            sa.VARCHAR(10),
            nullable=True,
            comment="Tenure Option Rollover",
        ),  # [4]
        sa.Column("flag", sa.VARCHAR(1), nullable=True, comment="Updation Flag"),  # [4]
        # Primary key is 'schemecode' based on the source [3]
        sa.PrimaryKeyConstraint("schemecode"),
    )


def downgrade():
    """
    Drops the fmp_yielddetails table.
    """
    op.drop_table("fmp_yielddetails")
