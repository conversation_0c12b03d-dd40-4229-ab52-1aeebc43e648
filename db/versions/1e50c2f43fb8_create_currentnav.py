"""create_currentnav

Revision ID: 1e50c2f43fb8
Revises: 5c82891661cf
Create Date: 2025-03-09 17:50:39.493992

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "1e50c2f43fb8"
down_revision: Union[str, None] = "5c82891661cf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "currentnav",
        sa.Column("schemecode", sa.BigInteger(), primary_key=True, nullable=False),
        sa.Column("navdate", sa.DateTime(), nullable=True),
        sa.Column("navrs", sa.Numeric(18, 6), nullable=True),
        sa.Column("repurprice", sa.Numeric(18, 6), nullable=True),
        sa.Column("saleprice", sa.Numeric(18, 6), nullable=True),
        sa.Column("cldate", sa.DateTime(), nullable=True),
        sa.Column("change", sa.Numeric(18, 6), nullable=True),
        sa.Column("netchange", sa.Numeric(18, 6), nullable=True),
        sa.Column("prevnav", sa.Numeric(18, 6), nullable=True),
        sa.Column("prenavdate", sa.DateTime(), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="currentnav_schemecode_idx",
        table_name="currentnav",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("currentnav")
