"""create_mf_portfolio

Revision ID: 6d8fe2d10cba
Revises: 155977297680
Create Date: 2025-03-09 17:34:23.736901

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "6d8fe2d10cba"
down_revision: Union[str, None] = "155977297680"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "mf_portfolio",
        sa.Column("schemecode", sa.BigInteger(), nullable=False),
        sa.Column("invdate", sa.DateTime(), nullable=False),
        sa.Column("invenddate", sa.DateTime(), nullable=True),
        sa.Column("srno", sa.BigInteger(), nullable=False),
        sa.Column("fincode", sa.<PERSON>(), nullable=True),
        sa.Column("asect_code", sa.BigInteger(), nullable=True),
        sa.Column("sect_code", sa.BigInteger(), nullable=True),
        sa.Column("noshares", sa.Numeric(precision=18, scale=0), nullable=True),
        sa.Column("mktval", sa.Numeric(18, 6), nullable=True),
        sa.Column("aum", sa.Numeric(18, 6), nullable=True),
        sa.Column("holdpercentage", sa.Numeric(18, 6), nullable=True),
        sa.Column("compname", sa.String(length=255), nullable=True),
        sa.Column("sect_name", sa.String(length=50), nullable=True),
        sa.Column("asect_name", sa.String(length=50), nullable=True),
        sa.Column("rating", sa.String(length=50), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "invdate", "srno"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="mf_portfolio_schemecode_idx",
        table_name="mf_portfolio",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_portfolio_fincode_idx",
        table_name="mf_portfolio",
        columns=["fincode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_portfolio_asect_code_idx",
        table_name="mf_portfolio",
        columns=["asect_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_portfolio_sect_code_idx",
        table_name="mf_portfolio",
        columns=["sect_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_portfolio_compname_idx",
        table_name="mf_portfolio",
        columns=["compname"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("mf_portfolio")
