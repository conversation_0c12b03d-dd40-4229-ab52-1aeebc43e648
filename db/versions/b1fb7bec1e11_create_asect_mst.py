"""create_asect_mst

Revision ID: b1fb7bec1e11
Revises: c3a431c4a1c0
Create Date: 2025-03-09 17:31:33.272188

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b1fb7bec1e11"
down_revision: Union[str, None] = "c3a431c4a1c0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "asect_mst",
        sa.Column("asect_code", sa.Numeric(18, 6), primary_key=True, nullable=False),
        sa.Column("asect_type", sa.String(length=100), nullable=True),
        sa.Column("asset", sa.String(length=50), nullable=True),
        sa.Column("as_name", sa.String(length=50), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="asect_mst_asect_code_idx",
        table_name="asect_mst",
        columns=["asect_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="asect_mst_asect_type_idx",
        table_name="asect_mst",
        columns=["asect_type"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="asect_mst_asset_idx",
        table_name="asect_mst",
        columns=["asset"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("asect_mst")
