"""create_mf_swp

Revision ID: a5182891f85d
Revises: 4764683b42e3
Create Date: 2025-03-09 17:06:13.028130

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "a5182891f85d"
down_revision: Union[str, None] = "4764683b42e3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "mf_swp",
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column(
            "amc_code",
            sa.BigInteger(),
            nullable=True,
        ),
        sa.Column("frequency", sa.String(length=100), nullable=False),
        sa.Column("swp", sa.String(length=1), nullable=True),
        sa.Column("swpdatescondition", sa.String(length=8000), nullable=True),
        sa.Column("dates", sa.String(length=8000), nullable=True),
        sa.Column("swpdaysall", sa.String(length=50), nullable=True),
        sa.Column("swpmininvest", sa.Numeric(18, 6), nullable=True),
        sa.Column("swpaddninvest", sa.Numeric(18, 6), nullable=True),
        sa.Column("swpfrequencyno", sa.Integer(), nullable=True),
        sa.Column("swpminimumperiod", sa.Integer(), nullable=True),
        sa.Column("swpmaximumperiod", sa.String(length=100), nullable=True),
        sa.Column("swpmincumamount", sa.String(length=100), nullable=True),
        sa.Column("swpminunits", sa.Numeric(18, 6), nullable=True),
        sa.Column("swpmultiplesunits", sa.Numeric(18, 6), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.PrimaryKeyConstraint("schemecode", "frequency"),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="mf_swp_schemecode_idx",
        table_name="mf_swp",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="mf_swp_amc_code_idx",
        table_name="mf_swp",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("mf_swp")
