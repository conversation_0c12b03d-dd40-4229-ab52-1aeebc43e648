"""create_mf_ans_return

Revision ID: 9a145dc7a64d
Revises: 830ebf4434e8
Create Date: 2025-04-27 11:31:18.455146

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "9a145dc7a64d"
down_revision: Union[str, None] = "830ebf4434e8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """
    Creates the mf_ans_return table based on the provided schema.
    This table contains scheme annualized returns data.
    """
    op.create_table(
        "mf_ans_return",  # Table name in lowercase as requested
        sa.Column(
            "schemecode",
            sa.BigInteger,
            primary_key=True,
            comment="Accord Fintech’s Scheme Code",
        ),  # Using BigInteger as requested, noted as Integer in source [5] but float in [2] - treating as Integer type code
        sa.Column(
            "c_date", sa.DateTime, nullable=True, comment="Closing Date"
        ),  # Datetime from source [9]
        sa.Column(
            "p_date", sa.DateTime, nullable=True, comment="Previous Date"
        ),  # Datetime from source [9]
        sa.Column(
            "c_nav", sa.Numeric(18, 6), nullable=True, comment="Closing Nav"
        ),  # Using Numeric(18,6) for Float from source [9]
        sa.Column(
            "p_nav", sa.Numeric(18, 6), nullable=True, comment="Previous Day Nav"
        ),  # Using Numeric(18,6) for Float from source [9]
        sa.Column(
            "1dayret", sa.Numeric(18, 6), nullable=True, comment="One Day Return"
        ),  # Using Numeric(18,6) for Float from source [9], corrected to match field name
        sa.Column(
            "1weekdate", sa.DateTime, nullable=True, comment="One Week Date"
        ),  # Datetime from source [9], corrected to match field name
        sa.Column(
            "1weeknav", sa.Numeric(18, 6), nullable=True, comment="One Week NAV"
        ),  # Using Numeric(18,6) for Float from source [9], corrected to match field name
        sa.Column(
            "1weekret", sa.Numeric(18, 6), nullable=True, comment="One Week Return"
        ),  # Using Numeric(18,6) for Float from source [9], corrected to match field name
        sa.Column(
            "1mthdate", sa.DateTime, nullable=True, comment="One Month Date"
        ),  # Datetime from source [9], corrected to match field name
        sa.Column(
            "1mthnav", sa.Numeric(18, 6), nullable=True, comment="One Month NAV"
        ),  # Using Numeric(18,6) for Float from source [9], corrected to match field name
        sa.Column(
            "1monthret", sa.Numeric(18, 6), nullable=True, comment="One Month Return"
        ),  # Using Numeric(18,6) for Float from source [9], corrected to match field name
        sa.Column(
            "3mthdate", sa.DateTime, nullable=True, comment="Three Month Date"
        ),  # Datetime from source [9], corrected to match field name
        sa.Column(
            "3mthnav", sa.Numeric(18, 6), nullable=True, comment="Three Month NAV"
        ),  # Using Numeric(18,6) for Float from source [9], corrected to match field name
        sa.Column(
            "3monthret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Three Month Return",
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "6mntdate", sa.DateTime, nullable=True, comment="Six Month Date"
        ),  # Datetime from source [10], corrected to match field name
        sa.Column(
            "6mnthnav", sa.Numeric(18, 6), nullable=True, comment="Six Month NAV"
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "6monthret", sa.Numeric(18, 6), nullable=True, comment="Six Month Return"
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "9mnthdate", sa.DateTime, nullable=True, comment="Nine Month Date"
        ),  # Datetime from source [10], corrected to match field name
        sa.Column(
            "9mnthnav", sa.Numeric(18, 6), nullable=True, comment="Nine Month NAV"
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "9mnthret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Nine Month Return",
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "1yrdate", sa.DateTime, nullable=True, comment="One Year Date"
        ),  # Datetime from source [10], corrected to match field name
        sa.Column(
            "1yrnav", sa.Numeric(18, 6), nullable=True, comment="One Year NAV"
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "1yrret", sa.Numeric(18, 6), nullable=True, comment="One Year Return"
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "2yrdate", sa.DateTime, nullable=True, comment="Two Year Date"
        ),  # Datetime from source [10], corrected to match field name
        sa.Column(
            "2yrnav", sa.Numeric(18, 6), nullable=True, comment="Two Year NAV"
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "2yearret", sa.Numeric(18, 6), nullable=True, comment="Two Year Return"
        ),  # Using Numeric(18,6) for Float from source [10], corrected to match field name
        sa.Column(
            "3yrdate", sa.DateTime, nullable=True, comment="Three Year Date"
        ),  # Datetime from source [11], corrected to match field name
        sa.Column(
            "3yrnav", sa.Numeric(18, 6), nullable=True, comment="Three Year NAV"
        ),  # Using Numeric(18,6) for Float from source [11], corrected to match field name
        sa.Column(
            "3yearret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Three Year Return",
        ),  # Using Numeric(18,6) for Float from source [11], corrected to match field name
        sa.Column(
            "4yrdate", sa.DateTime, nullable=True, comment="Four Year Date"
        ),  # Datetime from source [11], corrected to match field name
        sa.Column(
            "4yrnav", sa.Numeric(18, 6), nullable=True, comment="Four Year NAV"
        ),  # Using Numeric(18,6) for Float from source [11], corrected to match field name
        sa.Column(
            "4yearret", sa.Numeric(18, 6), nullable=True, comment="Four Year Return"
        ),  # Using Numeric(18,6) for Float from source [11], corrected to match field name
        sa.Column(
            "5yrdate", sa.DateTime, nullable=True, comment="Five Year Date"
        ),  # Datetime from source [11], corrected to match field name
        sa.Column(
            "5yrnav", sa.Numeric(18, 6), nullable=True, comment="Five Year NAV"
        ),  # Using Numeric(18,6) for Float from source [11], corrected to match field name
        sa.Column(
            "5yearret", sa.Numeric(18, 6), nullable=True, comment="Five Year Return"
        ),  # Using Numeric(18,6) for Float from source [11], corrected to match field name
        sa.Column(
            "incdate", sa.DateTime, nullable=True, comment="Inception date"
        ),  # Datetime from source [11]
        sa.Column(
            "incnav", sa.Numeric(18, 6), nullable=True, comment="NAV on inception date"
        ),  # Using Numeric(18,6) for Float from source [11]
        sa.Column(
            "incret",
            sa.Numeric(18, 6),
            nullable=True,
            comment="Return on inception date",
        ),  # Using Numeric(18,6) for Float from source [11]
        sa.Column(
            "flag", sa.VARCHAR(1), nullable=True, comment="Updation flag"
        ),  # Varchar(1) from source [11]
        # Primary key is 'schemecode' based on the source [2]
        sa.PrimaryKeyConstraint("schemecode"),
    )


def downgrade():
    """
    Drops the mf_ans_return table.
    """
    op.drop_table("mf_ans_return")
