from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, Request, Response, status
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.encoders import jsonable_encoder
from src.middleware.db import DbMiddleware
from fastapi.middleware.cors import CORSMiddleware
from src.middleware.request_ctx import RequestCtxMiddleware
from src.routers import (
    health_controller,
)

from config.cfg import cfg
from config.logger import log_error
from src.models import model
from src.exceptions import (
    ApiException,
    NotFoundException,
    PreconditionFailedException,
    BadRequestException,
    UnauthorizedException,
    ValueErrorException,
)
from uuid import uuid4
from src.routers.base_controller import (
    FailureResponseDAO,
    ResponseErrorsDAO,
    failure_response,
)
from fastapi.exceptions import RequestValidationError

INTERNAL_ERROR: str = "Internal Error"


def _initialize_routers(app: FastAPI) -> FastAPI:
    app.include_router(health_controller)
    return app


def _initialize_middleware(app: FastAPI) -> FastAPI:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(DbMiddleware)
    app.add_middleware(RequestCtxMiddleware)

    return app


def _initialize_exception_handlers(app: FastAPI) -> FastAPI:
    @app.exception_handler(ApiException)
    async def handle_api_exceptions(request: Request, exc: ApiException):
        error_id = str(uuid4())
        request_id = (
            str(request.state.ctx.request_id)
            if request.state.ctx and request.state.ctx.request_id
            else ""
        )
        log_error(
            msg=f"error_id={error_id} rid={request_id} ApiException: {exc}", e=exc
        )
        error_msg: str = exc.msg if cfg("ENV") == "development" else INTERNAL_ERROR
        return JSONResponse(
            status_code=exc.http_error_code,
            content=FailureResponseDAO(
                data={
                    "request_id": request_id,
                    "error_id": error_id,
                },
                api_version="v1.0",
                errors=ResponseErrorsDAO(msg=[error_msg], code=exc.code),
            ).model_dump(),
        )

    @app.exception_handler(NotFoundException)
    async def handle_api_exceptions(request: Request, exc: NotFoundException):
        error_id = str(uuid4())
        request_id = (
            str(request.state.ctx.request_id)
            if request.state and request.state.ctx and request.state.ctx.request_id
            else ""
        )
        log_error(
            msg=f"error_id={error_id} rid={request_id} NotFoundException: {exc}",
        )
        return JSONResponse(
            status_code=exc.http_error_code,
            content=FailureResponseDAO(
                data={
                    "request_id": request_id,
                    "error_id": error_id,
                },
                api_version="v1.0",
                errors=ResponseErrorsDAO(msg=[exc.msg], code=exc.code),
            ).model_dump(),
        )

    @app.exception_handler(BadRequestException)
    async def handle_api_exceptions(request: Request, exc: BadRequestException):
        error_id = str(uuid4())
        request_id = (
            str(request.state.ctx.request_id)
            if request.state and request.state.ctx and request.state.ctx.request_id
            else ""
        )
        log_error(
            msg=f"error_id={error_id} rid={request_id} BadRequestException: {exc}",
        )
        return JSONResponse(
            status_code=exc.http_error_code,
            content=FailureResponseDAO(
                data={
                    "request_id": request_id,
                    "error_id": error_id,
                },
                api_version="v1.0",
                errors=ResponseErrorsDAO(msg=[exc.msg], code=exc.code),
            ).model_dump(),
        )

    @app.exception_handler(PreconditionFailedException)
    async def handle_api_exceptions(request: Request, exc: PreconditionFailedException):
        error_id = str(uuid4())
        request_id = (
            str(request.state.ctx.request_id)
            if request.state and request.state.ctx and request.state.ctx.request_id
            else ""
        )
        log_error(
            msg=f"error_id={error_id} rid={request_id} PreconditionFailedException: {exc}",
        )
        return JSONResponse(
            status_code=exc.http_error_code,
            content=FailureResponseDAO(
                data={
                    "request_id": request_id,
                    "error_id": error_id,
                },
                api_version="v1.0",
                errors=ResponseErrorsDAO(msg=[exc.msg], code=exc.code),
            ).model_dump(),
        )

    @app.exception_handler(ValueErrorException)
    async def handle_api_exceptions(request: Request, exc: ValueErrorException):
        error_id = str(uuid4())
        request_id = (
            str(request.state.ctx.request_id)
            if request.state and request.state.ctx and request.state.ctx.request_id
            else ""
        )
        log_error(
            msg=f"error_id={error_id} rid={request_id} ValueErrorException: {exc}",
        )
        return JSONResponse(
            status_code=exc.http_error_code,
            content=FailureResponseDAO(
                data={
                    "request_id": request_id,
                    "error_id": error_id,
                },
                api_version="v1.0",
                errors=ResponseErrorsDAO(msg=[exc.msg], code=exc.code),
            ).model_dump(),
        )

    @app.exception_handler(UnauthorizedException)
    async def handle_api_exceptions(request: Request, exc: UnauthorizedException):
        error_id = str(uuid4())
        request_id = (
            str(request.state.ctx.request_id)
            if request.state and request.state.ctx and request.state.ctx.request_id
            else ""
        )
        log_error(
            msg=f"error_id={error_id} rid={request_id} UnauthorizedException: {exc}",
        )
        return JSONResponse(
            status_code=exc.http_error_code,
            content=FailureResponseDAO(
                data={
                    "request_id": request_id,
                    "error_id": error_id,
                },
                api_version="v1.0",
                errors=ResponseErrorsDAO(msg=[exc.msg], code=exc.code),
            ).model_dump(),
        )

    @app.exception_handler(RequestValidationError)
    async def handle_internal_errors(request: Request, exc: RequestValidationError):
        error_id = str(uuid4())
        request_id = str(request.state.ctx.request_id) if request.state.ctx else ""
        log_error(
            msg=f"error_id={error_id} rid={request_id} Unhandled Exception: {exc}",
            e=exc,
        )
        error_message: str = exc.errors()[0].get("msg") or "Validation Error"
        failure_dao: Dict[str, Any] = failure_response(
            error_code="bad_request",
            errors=[f"{error_message}"],
            payload={"error_id": error_id, "request_id": request_id},
        )
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=jsonable_encoder(failure_dao),
        )

    return app


def initialize_web_app(app: FastAPI) -> FastAPI:
    app = _initialize_routers(app=app)
    app = _initialize_middleware(app=app)
    app = _initialize_exception_handlers(app=app)
    # app.mount("/", StaticFiles(directory="public"))
    # app.mount("/", StaticFiles(directory="src/templates"))
    return app
