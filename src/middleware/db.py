from time import time
from typing import Callable, Any, Optional
from fastapi.requests import Request
from starlette.types import ASGIApp
from starlette.middleware.base import BaseHTTPMiddleware
from connectors.async_pg import get_async_session, async_scoped_session
from sqlalchemy.ext.asyncio import AsyncSession


class DbMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable):
        """
        Dispatches the request to the next middleware or endpoint, while managing the database session.

        Args:
            request (Request): The incoming request.
            call_next (Callable): The next middleware or endpoint to call.

        Returns:
            Any: The response from the next middleware or endpoint.
        """
        db: AsyncSession = await get_async_session()
        try:
            request.state.db = db
            response: Any = await call_next(request)
        finally:
            # Uncomment for async API
            if request.state.db and request.state.db.is_active:
                if isinstance(request.state.db, AsyncSession):
                    db_session: AsyncSession = request.state.db
                    await db_session.close()
                else:
                    request.state.db.close()
        return response
