"""companymaster

Revision ID: 8a19a4922f15
Revises: baf81ab65cfa
Create Date: 2025-03-09 17:28:01.354588

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "8a19a4922f15"
down_revision: Union[str, None] = "baf81ab65cfa"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "companymaster",
        sa.Column("fincode", sa.<PERSON>nteger(), primary_key=True, nullable=False),
        sa.Column("scripcode", sa.BigInteger(), nullable=True),
        sa.Column("symbol", sa.String(length=50), nullable=True),
        sa.Column("compname", sa.String(length=255), nullable=True),
        sa.Column("s_name", sa.String(length=100), nullable=True),
        sa.Column("ind_code", sa.Integer(), nullable=True),
        sa.Column("industry", sa.String(length=100), nullable=True),
        sa.Column("isin", sa.String(length=50), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=True),
        sa.Column("series", sa.String(length=2), nullable=True),
        sa.Column("listing", sa.String(length=50), nullable=True),
        sa.Column("sublisting", sa.String(length=50), nullable=True),
        sa.Column("fv", sa.Float(), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
    )
    op.execute("COMMIT;")
    op.create_index(
        index_name="companymaster_fincode_idx",
        table_name="companymaster",
        columns=["fincode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="companymaster_scripcode_idx",
        table_name="companymaster",
        columns=["scripcode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="companymaster_symbol_idx",
        table_name="companymaster",
        columns=["symbol"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="companymaster_compname_idx",
        table_name="companymaster",
        columns=["compname"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="companymaster_s_name_idx",
        table_name="companymaster",
        columns=["s_name"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="companymaster_ind_code_idx",
        table_name="companymaster",
        columns=["ind_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="companymaster_industry_idx",
        table_name="companymaster",
        columns=["industry"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="companymaster_status_idx",
        table_name="companymaster",
        columns=["status"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("companymaster")
