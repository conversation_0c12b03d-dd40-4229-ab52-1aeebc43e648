from config.cfg import cfg, is_dev, is_test
from celery import Celery
from celery.schedules import crontab
from celery.utils.log import get_logger
from redbeat import RedBeatScheduler
import asyncio
from asyncio import AbstractEventLoop
import ssl
import socket

celery: Celery = None

if cfg("ENV") == "prod":
    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLSv1_2)
    ssl_context.verify_mode = ssl.CERT_REQUIRED
    celery = Celery(
        __name__,
        broker=cfg("REDIS_URL"),
        broker_use_ssl={
            "ssl_cert_reqs": ssl.CERT_REQUIRED,
        },
        redis_backend_use_ssl={
            "ssl_cert_reqs": ssl.CERT_REQUIRED,
        },
        include=[
            "src.tasks.incremental_import_worker",
        ],
    )
else:
    celery = Celery(
        __name__,
        broker=cfg("REDIS_URL"),
        include=[
            "src.tasks.incremental_import_worker",
        ],
    )

celery.conf.timezone = "UTC"  # Set to your desired timezone
celery.conf.worker_log_level = "INFO"
celery.conf.broker_url = cfg("REDIS_URL")
celery.conf.result_backend = cfg("REDIS_URL")
socket_keepalive_options = {
    # This is the primary timer. It defines how long the connection must be idle (i.e., no data sent or received) before the operating system starts sending keepalive probes.
    # This is the number of keepalive probes that can be sent before the connection is considered dead.
    socket.TCP_KEEPCNT: 5,
    # This is the interval between keepalive probes.
    socket.TCP_KEEPINTVL: 10,
}
if cfg("ENV") == "prod":
    socket_keepalive_options.update(
        {
            socket.TCP_KEEPIDLE: 20,
        }
    )

celery.conf.broker_transport_options = {
    # This is the timeout for the visibility of the task. Visibility means that the task is not being worked on by any worker but is still in the queue.
    "visibility_timeout": 21600,
    # This is the interval for the health check of the worker.
    "health_check_interval": 10,
    # This is the order of the queue.
    "queue_order_strategy": "priority",
    "priority_steps": list(range(10)),
    # This is the retry policy for the task. If the task fails, it will be retried after the timeout.
    "retry_policy": {
        "timeout": 90.0,  # Increase timeout for retries
        "max_retries": 20,  # Increase retry attempts
        "interval_start": 0,  # Start retry immediately
        "interval_step": 1,  # Increase delay by 1 second per retry
        "interval_max": 5,  # Max delay between retries
    },
    # This is the keepalive option for the socket.
    "socket_keepalive": True,
    "socket_keepalive_options": socket_keepalive_options,
}

celery.conf.result_backend_transport_options = {
    "retry_policy": {
        "timeout": 90.0,  # Increase timeout for retries
        "max_retries": 20,  # Increase retry attempts
        "interval_start": 0,  # Start retry immediately
        "interval_step": 1,  # Increase delay by 1 second per retry
        "interval_max": 5,  # Max delay between retries
    },
    "socket_keepalive": True,
    "socket_keepalive_options": socket_keepalive_options,
}


always_eager = False


celery.conf.update(
    broker_url=cfg("REDIS_URL"),
    result_backend=cfg("REDIS_URL"),
    task_serializer="json",
    result_serializer="json",
    accept_content=["json"],
    timezone="UTC",
    task_default_priority=1,
    worker_prefetch_multiplier=1,
    broker_connection_retry_on_startup=True,
    log_level="INFO",
)


celery.conf.beat_scheduler = RedBeatScheduler
celery.conf.redbeat_lock_timeout = 300  # Increase lock timeout to 5 minutes
celery.conf.redbeat_redis_url = cfg("REDIS_URL")

celery.conf.task_routes = {
    "incremental_import_worker": {"queue": "incremental_import_worker_queue"},
}


celery.conf.beat_schedule = {
    # Schedule the incremental import worker to run daily at 2:30 PM UTC which IS 8:00 PM IST
    "incremental_import_worker": {
        "task": "incremental_import_worker",
        "schedule": crontab(minute="30", hour="14", day_of_week="*", day_of_month="*"),
        "options": {"queue": "incremental_import_worker_queue"},
    },
}

logger = get_logger(__name__)
logger.info("Celery initialised")
