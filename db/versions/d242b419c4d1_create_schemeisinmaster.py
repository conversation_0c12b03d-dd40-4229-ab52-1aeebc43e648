"""create_schemeisinmaster

Revision ID: d242b419c4d1
Revises: 295ac8345827
Create Date: 2025-03-09 16:29:01.559616

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "d242b419c4d1"
down_revision: Union[str, None] = "295ac8345827"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.create_table(
        "schemeisinmaster",
        sa.Column("id", sa.BigInteger(), nullable=True),
        sa.Column("isin", sa.String(length=100), primary_key=True, nullable=False),
        sa.Column(
            "schemecode",
            sa.BigInteger(),
            nullable=True,
        ),
        sa.Column(
            "amc_code",
            sa.<PERSON>ger(),
            nullable=True,
        ),
        sa.Column("nsesymbol", sa.String(length=100), nullable=True),
        sa.Column("series", sa.String(length=50), nullable=True),
        sa.Column("rtaschemecode", sa.String(length=50), nullable=True),
        sa.Column("flag", sa.String(length=1), nullable=True),
        sa.Column("amcschemecode", sa.String(length=50), nullable=True),
        sa.Column("longschemedescrip", sa.String(length=255), nullable=True),
        sa.Column("shortschemedescrip", sa.String(length=255), nullable=True),
        sa.Column("status", sa.String(length=10), nullable=True),
    )

    op.execute("COMMIT;")
    op.create_index(
        index_name="schemeisinmaster_isin_idx",
        table_name="schemeisinmaster",
        columns=["isin"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="schemeisinmaster_schemecode_idx",
        table_name="schemeisinmaster",
        columns=["schemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="schemeisinmaster_amc_code_idx",
        table_name="schemeisinmaster",
        columns=["amc_code"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="schemeisinmaster_nsesymbol_idx",
        table_name="schemeisinmaster",
        columns=["nsesymbol"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="schemeisinmaster_rtaschemecode_idx",
        table_name="schemeisinmaster",
        columns=["rtaschemecode"],
        postgresql_concurrently=True,
    )
    op.create_index(
        index_name="schemeisinmaster_series_idx",
        table_name="schemeisinmaster",
        columns=["series"],
        postgresql_concurrently=True,
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table("schemeisinmaster")
